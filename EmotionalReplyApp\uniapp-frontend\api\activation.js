import { post, get, request } from '../utils/request.js'

/**
 * 生成激活码（管理员功能）
 * @param {string} codeType - 激活码类型
 * @param {number} durationDays - 有效天数
 * @param {number} adminId - 管理员ID
 * @param {string} remark - 备注
 * @returns {Promise} 生成结果
 */
export const generateActivationCode = (codeType, durationDays, adminId, remark) => {
  return post('/activation-code/generate', {
    codeType,
    durationDays,
    adminId,
    remark
  })
}

/**
 * 使用激活码
 * @param {string} code - 激活码
 * @param {number} userId - 用户ID
 * @returns {Promise} 使用结果
 */
export const useActivationCode = (code, userId) => {
  // 使用request方法直接发送，因为需要传递查询参数
  // 禁用全局错误处理，让页面自己处理错误提示
  return request({
    url: `/activation-code/use?code=${encodeURIComponent(code)}&userId=${userId}`,
    method: 'POST',
    skipGlobalError: true
  })
}

/**
 * 验证激活码
 * @param {string} code - 激活码
 * @returns {Promise} 验证结果
 */
export const validateActivationCode = (code) => {
  return get('/activation-code/validate', {
    code
  })
}

/**
 * 获取激活码列表（管理员功能）
 * @param {number} status - 状态筛选
 * @param {string} codeType - 类型筛选
 * @returns {Promise} 激活码列表
 */
export const getActivationCodeList = (status, codeType) => {
  const params = {}
  if (status !== undefined && status !== null && status >= 0) {
    params.status = status
  }
  if (codeType && codeType.trim()) {
    params.codeType = codeType
  }
  return get('/activation-code/list', params)
}

/**
 * 禁用激活码（管理员功能）
 * @param {number} codeId - 激活码ID
 * @param {number} adminId - 管理员ID
 * @returns {Promise} 禁用结果
 */
export const disableActivationCode = (codeId, adminId) => {
  return post(`/activation-code/disable/${codeId}`, {
    adminId
  })
}

export default {
  generateActivationCode,
  useActivationCode,
  validateActivationCode,
  getActivationCodeList,
  disableActivationCode
}
