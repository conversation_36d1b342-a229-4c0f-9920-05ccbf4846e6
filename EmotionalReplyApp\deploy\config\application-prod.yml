server:
  port: 8080
  servlet:
    context-path: /api
  tomcat:
    max-threads: 200
    min-spare-threads: 10

spring:
  profiles:
    active: prod
  
  datasource:
    url: jdbc:mysql://${DB_HOST:mysql}:${DB_PORT:3306}/${DB_NAME:emotional_reply}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
    username: ${DB_USERNAME:emotional_reply}
    password: ${DB_PASSWORD}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000

  redis:
    host: ${REDIS_HOST:redis}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms

  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: false

  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss

# JWT配置
jwt:
  secret: ${JWT_SECRET}
  expiration: 86400000  # 24小时

# 应用配置
app:
  name: 情感回复助手
  version: 1.0.0
  upload:
    path: /opt/emotional-reply/uploads
    max-size: 10485760  # 10MB

# 日志配置
logging:
  level:
    root: INFO
    com.emotional.service: INFO
    org.springframework.security: WARN
  file:
    name: /var/log/emotional-reply/application.log
  logback:
    rollingpolicy:
      max-file-size: 100MB
      max-history: 30
      total-size-cap: 1GB

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

# 第三方服务配置
emotion:
  api:
    url: ${EMOTION_API_URL:}
    key: ${EMOTION_API_KEY:}
    timeout: 10000

# 邮件配置
mail:
  host: ${MAIL_HOST:}
  port: ${MAIL_PORT:587}
  username: ${MAIL_USERNAME:}
  password: ${MAIL_PASSWORD:}
  properties:
    mail:
      smtp:
        auth: true
        starttls:
          enable: true
