#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const CURRENT_DB_FILE = path.join(__dirname, '../src/services/databaseService.js');
const MULTI_DB_FILE = path.join(__dirname, '../src/services/databaseService_multi.js');
const BACKUP_FILE = path.join(__dirname, '../src/services/databaseService_backup.js');

function showCurrentMode() {
    console.log('📊 当前数据库模式：多数据库模式');
    console.log('✅ 激活码数据库：emotional_reply_db');
    console.log('✅ 关键词数据库：wechat_activation_bot');
    console.log('');
    console.log('📝 配置文件：.env');
    console.log('   ACTIVATION_DB_NAME=emotional_reply_db');
    console.log('   KEYWORD_DB_NAME=wechat_activation_bot');
}

function backupCurrentConfig() {
    console.log('💾 备份当前数据库配置...');

    try {
        if (fs.existsSync(CURRENT_DB_FILE)) {
            fs.copyFileSync(CURRENT_DB_FILE, BACKUP_FILE);
            console.log('✅ 已备份当前数据库服务配置');
            return true;
        } else {
            console.error('❌ 当前数据库服务文件不存在');
            return false;
        }
    } catch (error) {
        console.error('❌ 备份失败:', error.message);
        return false;
    }
}

function restoreConfig() {
    console.log('🔄 恢复数据库配置...');

    try {
        if (fs.existsSync(BACKUP_FILE)) {
            fs.copyFileSync(BACKUP_FILE, CURRENT_DB_FILE);
            console.log('✅ 已恢复数据库服务配置');
            return true;
        } else {
            console.error('❌ 备份文件不存在');
            return false;
        }
    } catch (error) {
        console.error('❌ 恢复失败:', error.message);
        return false;
    }
}

function checkDatabaseFiles() {
    console.log('🔍 检查数据库文件状态...');

    console.log(`📄 当前数据库服务: ${fs.existsSync(CURRENT_DB_FILE) ? '✅ 存在' : '❌ 不存在'}`);
    console.log(`📄 多数据库模板: ${fs.existsSync(MULTI_DB_FILE) ? '✅ 存在' : '❌ 不存在'}`);
    console.log(`📄 备份文件: ${fs.existsSync(BACKUP_FILE) ? '✅ 存在' : '❌ 不存在'}`);
}

function showStatus() {
    console.log('📊 微信激活码机器人 - 数据库状态');
    console.log('');
    showCurrentMode();
    console.log('');
    checkDatabaseFiles();
    console.log('');
    console.log('可用命令：');
    console.log('  node scripts/switch-database.js status  - 查看当前状态');
    console.log('  node scripts/switch-database.js backup  - 备份当前配置');
    console.log('  node scripts/switch-database.js restore - 恢复备份配置');
    console.log('  node scripts/switch-database.js check   - 检查文件状态');
}

// 主函数
function main() {
    const command = process.argv[2];

    switch (command) {
        case 'backup':
            backupCurrentConfig();
            break;

        case 'restore':
            restoreConfig();
            break;

        case 'check':
            checkDatabaseFiles();
            break;

        case 'status':
            showStatus();
            break;

        default:
            console.log('🤖 微信激活码机器人 - 数据库管理工具');
            console.log('');
            console.log('当前系统使用多数据库模式，无需切换。');
            console.log('');
            showStatus();
            break;
    }
}

if (require.main === module) {
    main();
}

module.exports = { showCurrentMode, backupCurrentConfig, restoreConfig, checkDatabaseFiles, showStatus };
