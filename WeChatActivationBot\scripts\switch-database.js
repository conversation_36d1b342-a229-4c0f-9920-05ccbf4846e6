#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const DEMO_DB_FILE = path.join(__dirname, '../src/services/databaseService.js');
const MYSQL_DB_FILE = path.join(__dirname, '../src/services/databaseService_mysql.js');
const DEMO_BACKUP_FILE = path.join(__dirname, '../src/services/databaseService_demo.js');

function switchToMySQL() {
    console.log('🔄 切换到MySQL数据库模式...');
    
    try {
        // 备份当前的演示版本
        if (fs.existsSync(DEMO_DB_FILE)) {
            fs.copyFileSync(DEMO_DB_FILE, DEMO_BACKUP_FILE);
            console.log('✅ 已备份演示版数据库服务');
        }
        
        // 复制MySQL版本
        if (fs.existsSync(MYSQL_DB_FILE)) {
            fs.copyFileSync(MYSQL_DB_FILE, DEMO_DB_FILE);
            console.log('✅ 已切换到MySQL数据库服务');
        } else {
            console.error('❌ MySQL数据库服务文件不存在');
            return false;
        }
        
        console.log('🎉 切换完成！现在使用MySQL数据库');
        console.log('📝 请确保：');
        console.log('   1. MySQL服务已启动');
        console.log('   2. .env文件中的数据库配置正确');
        console.log('   3. 数据库用户有创建数据库的权限');
        
        return true;
    } catch (error) {
        console.error('❌ 切换失败:', error.message);
        return false;
    }
}

function switchToDemo() {
    console.log('🔄 切换到演示模式（内存数据库）...');
    
    try {
        // 恢复演示版本
        if (fs.existsSync(DEMO_BACKUP_FILE)) {
            fs.copyFileSync(DEMO_BACKUP_FILE, DEMO_DB_FILE);
            console.log('✅ 已恢复演示版数据库服务');
        } else {
            console.error('❌ 演示版备份文件不存在');
            return false;
        }
        
        console.log('🎉 切换完成！现在使用内存数据库（演示模式）');
        console.log('📝 注意：重启服务器后数据会重置');
        
        return true;
    } catch (error) {
        console.error('❌ 切换失败:', error.message);
        return false;
    }
}

function showStatus() {
    console.log('📊 当前数据库模式状态：');
    
    try {
        const content = fs.readFileSync(DEMO_DB_FILE, 'utf8');
        
        if (content.includes('mysql2/promise')) {
            console.log('✅ 当前模式：MySQL数据库（真实数据）');
        } else if (content.includes('内存存储')) {
            console.log('✅ 当前模式：内存数据库（演示模式）');
        } else {
            console.log('❓ 当前模式：未知');
        }
        
        console.log('\n可用的切换选项：');
        console.log('  node scripts/switch-database.js mysql  - 切换到MySQL');
        console.log('  node scripts/switch-database.js demo   - 切换到演示模式');
        console.log('  node scripts/switch-database.js status - 查看当前状态');
        
    } catch (error) {
        console.error('❌ 无法读取数据库服务文件:', error.message);
    }
}

// 主函数
function main() {
    const command = process.argv[2];
    
    switch (command) {
        case 'mysql':
            if (switchToMySQL()) {
                process.exit(0);
            } else {
                process.exit(1);
            }
            break;
            
        case 'demo':
            if (switchToDemo()) {
                process.exit(0);
            } else {
                process.exit(1);
            }
            break;
            
        case 'status':
            showStatus();
            break;
            
        default:
            console.log('🤖 微信激活码机器人 - 数据库切换工具');
            console.log('');
            console.log('使用方法:');
            console.log('  node scripts/switch-database.js mysql  - 切换到MySQL数据库');
            console.log('  node scripts/switch-database.js demo   - 切换到演示模式');
            console.log('  node scripts/switch-database.js status - 查看当前状态');
            console.log('');
            showStatus();
            break;
    }
}

if (require.main === module) {
    main();
}

module.exports = { switchToMySQL, switchToDemo, showStatus };
