const mysql = require('mysql2/promise');
const Logger = require('../utils/logger');

class DatabaseService {
    constructor() {
        this.activationConnection = null;  // 激活码数据库连接
        this.keywordConnection = null;     // 关键词数据库连接
        
        this.baseConfig = {
            host: process.env.DB_HOST || 'localhost',
            port: process.env.DB_PORT || 3306,
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || '',
            charset: 'utf8mb4'
        };
        
        this.activationDbName = process.env.ACTIVATION_DB_NAME || 'emotional_reply_db';
        this.keywordDbName = process.env.KEYWORD_DB_NAME || 'wechat_activation_bot';
    }

    /**
     * 初始化数据库连接
     */
    async initialize() {
        try {
            // 连接到激活码数据库
            this.activationConnection = await mysql.createConnection({
                ...this.baseConfig,
                database: this.activationDbName
            });
            Logger.info(`已连接到激活码数据库: ${this.activationDbName}`);

            // 连接到关键词数据库（如果不同的话）
            if (this.keywordDbName !== this.activationDbName) {
                // 先检查关键词数据库是否存在，不存在则创建
                const tempConnection = await mysql.createConnection(this.baseConfig);
                await tempConnection.execute(`CREATE DATABASE IF NOT EXISTS \`${this.keywordDbName}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
                await tempConnection.end();

                this.keywordConnection = await mysql.createConnection({
                    ...this.baseConfig,
                    database: this.keywordDbName
                });
                Logger.info(`已连接到关键词数据库: ${this.keywordDbName}`);
            } else {
                // 如果是同一个数据库，使用相同的连接
                this.keywordConnection = this.activationConnection;
                Logger.info('激活码和关键词使用同一个数据库');
            }

            // 确保关键词表存在
            await this.ensureKeywordTableExists();
            
            // 检查激活码表结构
            await this.checkActivationCodeTable();
            
        } catch (error) {
            Logger.error('数据库初始化失败:', error);
            throw error;
        }
    }

    /**
     * 确保关键词表存在
     */
    async ensureKeywordTableExists() {
        try {
            await this.keywordConnection.execute(`
                CREATE TABLE IF NOT EXISTS keywords (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    keyword VARCHAR(255) UNIQUE NOT NULL,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_keyword (keyword)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            `);
            Logger.info('关键词表检查完成');
        } catch (error) {
            Logger.error('创建关键词表失败:', error);
        }
    }

    /**
     * 检查激活码表结构
     */
    async checkActivationCodeTable() {
        try {
            const [columns] = await this.activationConnection.execute(`
                SELECT COLUMN_NAME 
                FROM information_schema.COLUMNS 
                WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'activation_codes'
            `, [this.activationDbName]);
            
            const columnNames = columns.map(col => col.COLUMN_NAME);
            Logger.info('激活码表字段:', columnNames);
            
            // 检查总数
            const [count] = await this.activationConnection.execute('SELECT COUNT(*) as total FROM activation_codes');
            Logger.info(`激活码表中共有 ${count[0].total} 条记录`);
            
        } catch (error) {
            Logger.error('检查激活码表失败:', error);
        }
    }

    // ==================== 关键字相关方法 ====================

    async getAllKeywords() {
        try {
            const [rows] = await this.keywordConnection.execute('SELECT * FROM keywords ORDER BY created_at DESC');
            
            // 为每个关键字添加激活码统计
            for (let keyword of rows) {
                const stats = await this.getActivationCodeStats(keyword.id);
                keyword.activation_code_stats = stats;
            }
            
            return rows;
        } catch (error) {
            Logger.error('获取关键字失败:', error);
            return [];
        }
    }

    async getKeywordById(id) {
        try {
            const [rows] = await this.keywordConnection.execute('SELECT * FROM keywords WHERE id = ?', [id]);
            const keyword = rows[0];
            if (keyword) {
                keyword.activation_code_stats = await this.getActivationCodeStats(id);
            }
            return keyword;
        } catch (error) {
            Logger.error('获取关键字失败:', error);
            return null;
        }
    }

    async addKeyword(keyword, description = '') {
        try {
            const [result] = await this.keywordConnection.execute(
                'INSERT INTO keywords (keyword, description) VALUES (?, ?)', 
                [keyword, description]
            );
            return await this.getKeywordById(result.insertId);
        } catch (error) {
            Logger.error('添加关键字失败:', error);
            throw error;
        }
    }

    async removeKeyword(id) {
        try {
            const [result] = await this.keywordConnection.execute('DELETE FROM keywords WHERE id = ?', [id]);
            return result;
        } catch (error) {
            Logger.error('删除关键字失败:', error);
            throw error;
        }
    }

    // ==================== 激活码相关方法 ====================

    async getAvailableActivationCode(keywordId) {
        try {
            // 从激活码数据库获取未使用的激活码
            const [rows] = await this.activationConnection.execute(`
                SELECT * FROM activation_codes 
                WHERE (used_by IS NULL OR used_by = '') 
                ORDER BY id ASC 
                LIMIT 1
            `);
            return rows[0];
        } catch (error) {
            Logger.error('获取可用激活码失败:', error);
            return null;
        }
    }

    async assignActivationCode(codeId, userId) {
        try {
            const [result] = await this.activationConnection.execute(
                'UPDATE activation_codes SET used_by = ?, used_at = NOW() WHERE id = ?', 
                [userId, codeId]
            );
            return result;
        } catch (error) {
            Logger.error('分配激活码失败:', error);
            throw error;
        }
    }

    async getUserActivationCode(keywordId, userId) {
        try {
            const [rows] = await this.activationConnection.execute(`
                SELECT * FROM activation_codes 
                WHERE used_by = ?
            `, [userId]);
            return rows[0];
        } catch (error) {
            Logger.error('获取用户激活码失败:', error);
            return null;
        }
    }

    async batchInsertActivationCodes(keywordId, codes) {
        if (codes.length === 0) return [];
        
        try {
            const placeholders = codes.map(() => '(?)').join(', ');
            const [result] = await this.activationConnection.execute(
                `INSERT INTO activation_codes (code) VALUES ${placeholders}`,
                codes
            );
            
            const results = [];
            for (let i = 0; i < codes.length; i++) {
                results.push({
                    id: result.insertId + i,
                    code: codes[i]
                });
            }
            return results;
        } catch (error) {
            Logger.error('批量插入激活码失败:', error);
            throw error;
        }
    }

    async getActivationCodesByKeyword(keywordId) {
        try {
            // 返回所有激活码（因为激活码表中没有keyword_id字段）
            const [rows] = await this.activationConnection.execute(
                'SELECT * FROM activation_codes ORDER BY id DESC'
            );
            return rows;
        } catch (error) {
            Logger.error('获取激活码失败:', error);
            return [];
        }
    }

    async getAllActivationCodes() {
        try {
            const [rows] = await this.activationConnection.execute(
                'SELECT * FROM activation_codes ORDER BY id DESC'
            );
            return rows;
        } catch (error) {
            Logger.error('获取所有激活码失败:', error);
            return [];
        }
    }

    async getActivationCodeByCode(code) {
        try {
            const [rows] = await this.activationConnection.execute('SELECT * FROM activation_codes WHERE code = ?', [code]);
            return rows[0];
        } catch (error) {
            Logger.error('根据代码获取激活码失败:', error);
            return null;
        }
    }

    async addActivationCode(keywordId, code) {
        try {
            const [result] = await this.activationConnection.execute(
                'INSERT INTO activation_codes (code) VALUES (?)', 
                [code]
            );
            return { id: result.insertId, code };
        } catch (error) {
            Logger.error('添加激活码失败:', error);
            throw error;
        }
    }

    async removeActivationCode(id) {
        try {
            const [result] = await this.activationConnection.execute('DELETE FROM activation_codes WHERE id = ?', [id]);
            return result;
        } catch (error) {
            Logger.error('删除激活码失败:', error);
            throw error;
        }
    }

    async getActivationCodeStats(keywordId) {
        try {
            const [rows] = await this.activationConnection.execute(`
                SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN used_by IS NOT NULL AND used_by != '' THEN 1 END) as used
                FROM activation_codes
            `);
            return rows[0];
        } catch (error) {
            Logger.error('获取激活码统计失败:', error);
            return { total: 0, used: 0 };
        }
    }

    async resetActivationCodes(keywordId) {
        try {
            const [result] = await this.activationConnection.execute(
                'UPDATE activation_codes SET used_by = NULL, used_at = NULL'
            );
            return result.affectedRows;
        } catch (error) {
            Logger.error('重置激活码失败:', error);
            throw error;
        }
    }

    /**
     * 关闭数据库连接
     */
    async close() {
        if (this.activationConnection) {
            await this.activationConnection.end();
            Logger.info('激活码数据库连接已关闭');
        }
        if (this.keywordConnection && this.keywordConnection !== this.activationConnection) {
            await this.keywordConnection.end();
            Logger.info('关键词数据库连接已关闭');
        }
    }

    /**
     * 测试数据库连接
     */
    async testConnection() {
        try {
            await this.activationConnection.ping();
            if (this.keywordConnection !== this.activationConnection) {
                await this.keywordConnection.ping();
            }
            return true;
        } catch (error) {
            Logger.error('数据库连接测试失败:', error);
            return false;
        }
    }
}

module.exports = DatabaseService;
