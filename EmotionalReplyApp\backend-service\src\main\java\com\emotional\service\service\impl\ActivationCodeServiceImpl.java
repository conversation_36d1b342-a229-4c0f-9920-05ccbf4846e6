package com.emotional.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.emotional.service.entity.ActivationCode;
import com.emotional.service.entity.User;
import com.emotional.service.dto.ActivationCodeDTO;
import com.emotional.service.mapper.ActivationCodeMapper;
import com.emotional.service.service.ActivationCodeService;
import com.emotional.service.service.UserService;
import com.emotional.service.service.UserStatsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 激活码服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ActivationCodeServiceImpl extends ServiceImpl<ActivationCodeMapper, ActivationCode> implements ActivationCodeService {

    private final UserService userService;
    private final UserStatsService userStatsService;
    
    private static final String CHAR_SET = "23456789ABCDEFGHJKMNPQRSTUVWXYZ";
    
    @Override
    public ActivationCode generateActivationCode(String codeType, Integer durationDays, Long createdBy, String remark) {
        try {
            ActivationCode activationCode = new ActivationCode();
            activationCode.setCode(generateUniqueCode());
            activationCode.setCodeType(codeType);
            activationCode.setDurationDays(durationDays);
            activationCode.setBatchId(UUID.randomUUID().toString());
            activationCode.setCreatedBy(createdBy);
            // 激活码永久有效，不设置过期时间
            activationCode.setStatus(0); // 未使用
            activationCode.setRemark(remark);
            
            if (this.save(activationCode)) {
                log.info("生成激活码成功: code={}, type={}, createdBy={}", 
                        activationCode.getCode(), codeType, createdBy);
                return activationCode;
            } else {
                throw new RuntimeException("保存激活码失败");
            }
        } catch (Exception e) {
            log.error("生成激活码失败", e);
            throw new RuntimeException("生成激活码失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional
    public boolean useActivationCode(String code, Long userId) {
        try {
            // 查找激活码
            ActivationCode activationCode = baseMapper.findByCodeAndUnused(code);

            if (activationCode == null) {
                // 检查激活码是否存在但已被使用
                ActivationCode existingCode = baseMapper.findByCode(code);
                if (existingCode != null) {
                    log.warn("激活码已被使用: code={}, usedBy={}, usedTime={}",
                            code, existingCode.getUsedBy(), existingCode.getUsedTime());
                    throw new RuntimeException("激活码已被使用");
                } else {
                    log.warn("激活码不存在: code={}", code);
                    throw new RuntimeException("激活码不存在或格式错误");
                }
            }
            
            // 激活码永久有效，不需要检查过期时间
            
            // 获取用户信息
            User user = userService.getUserById(userId);
            if (user == null) {
                log.warn("用户不存在: userId={}", userId);
                throw new RuntimeException("用户不存在");
            }
            
            // 更新用户VIP状态和配额
            LocalDateTime newVipExpireTime = calculateNewVipExpireTime(user, activationCode.getDurationDays());

            log.info("更新前用户状态: userId={}, isVip={}, dailyQuota={}, vipExpireTime={}",
                    userId, user.getIsVip(), user.getDailyQuota(), user.getVipExpireTime());

            user.setIsVip(1);
            user.setVipExpireTime(newVipExpireTime);
            user.setDailyQuota(50); // 重要：设置VIP配额为50

            log.info("准备更新用户信息: userId={}, isVip=1, dailyQuota=50, vipExpireTime={}",
                    userId, newVipExpireTime);

            if (!userService.updateById(user)) {
                throw new RuntimeException("更新用户VIP状态失败");
            }

            // 验证更新结果
            User updatedUser = userService.getUserById(userId);
            log.info("更新后用户状态: userId={}, isVip={}, dailyQuota={}, vipExpireTime={}",
                    userId, updatedUser.getIsVip(), updatedUser.getDailyQuota(), updatedUser.getVipExpireTime());

            // 检查配额是否正确更新
            if (updatedUser.getDailyQuota() == null || !updatedUser.getDailyQuota().equals(50)) {
                log.error("配额更新失败! 期望值: 50, 实际值: {}", updatedUser.getDailyQuota());
                throw new RuntimeException("配额更新失败，期望值50，实际值: " + updatedUser.getDailyQuota());
            }

            // 重要：同时更新用户统计表中的今日配额
            try {
                userStatsService.updateUserQuotaAfterVipActivation(userId);
                log.info("用户统计表配额已更新: userId={}", userId);
            } catch (Exception e) {
                log.warn("更新用户统计表配额失败，但不影响VIP激活: userId={}, error={}", userId, e.getMessage());
            }

            // 标记激活码为已使用
            activationCode.setStatus(1);
            activationCode.setUsedBy(userId);
            activationCode.setUsedTime(LocalDateTime.now());
            
            if (!this.updateById(activationCode)) {
                throw new RuntimeException("更新激活码状态失败");
            }
            
            log.info("激活码使用成功: code={}, userId={}, newVipExpireTime={}", 
                    code, userId, newVipExpireTime);
            
            return true;
            
        } catch (Exception e) {
            log.error("使用激活码失败: code={}, userId={}", code, userId, e);
            return false;
        }
    }
    
    @Override
    public boolean isValidCode(String code) {
        ActivationCode activationCode = baseMapper.findByCodeAndUnused(code);
        if (activationCode == null) {
            return false;
        }
        
        // 激活码永久有效，只检查是否已使用
        return activationCode.getStatus() == 0;
    }

    @Override
    public List<ActivationCode> getActivationCodeList(Integer status, String codeType) {
        try {
            QueryWrapper<ActivationCode> queryWrapper = new QueryWrapper<>();

            // 状态筛选
            if (status != null && status >= 0) {
                queryWrapper.eq("status", status);
            }

            // 类型筛选
            if (codeType != null && !codeType.trim().isEmpty()) {
                queryWrapper.eq("code_type", codeType);
            }

            // 按创建时间倒序排列
            queryWrapper.orderByDesc("created_time");

            List<ActivationCode> activationCodes = this.list(queryWrapper);

            log.info("获取激活码列表成功，共{}条记录", activationCodes.size());

            return activationCodes;

        } catch (Exception e) {
            log.error("获取激活码列表失败", e);
            throw new RuntimeException("获取激活码列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成唯一激活码
     */
    private String generateUniqueCode() {
        String code;
        int attempts = 0;
        do {
            code = generateRandomCode();
            attempts++;
            if (attempts > 100) {
                throw new RuntimeException("生成唯一激活码失败，尝试次数过多");
            }
        } while (baseMapper.existsByCode(code));
        
        return code;
    }
    
    /**
     * 生成随机激活码
     */
    private String generateRandomCode() {
        StringBuilder sb = new StringBuilder();
        Random random = new Random();
        
        for (int i = 0; i < 16; i++) {
            if (i > 0 && i % 4 == 0) {
                sb.append("-");
            }
            sb.append(CHAR_SET.charAt(random.nextInt(CHAR_SET.length())));
        }
        
        return sb.toString();
    }
    
    /**
     * 计算新的VIP过期时间
     */
    private LocalDateTime calculateNewVipExpireTime(User user, Integer durationDays) {
        LocalDateTime currentVipExpire = user.getVipExpireTime();
        LocalDateTime now = LocalDateTime.now();

        // 如果当前VIP还未过期，从过期时间开始计算
        if (currentVipExpire != null && currentVipExpire.isAfter(now)) {
            return currentVipExpire.plusDays(durationDays);
        } else {
            // 如果已过期或没有VIP，从现在开始计算
            return now.plusDays(durationDays);
        }
    }

    @Override
    @Transactional
    public boolean disableActivationCode(Long codeId, Long adminId) {
        try {
            // 查找激活码
            ActivationCode activationCode = this.getById(codeId);
            if (activationCode == null) {
                log.warn("激活码不存在: codeId={}", codeId);
                return false;
            }

            // 检查激活码状态
            if (activationCode.getStatus() == 1) {
                log.warn("激活码已被使用，无法禁用: codeId={}, code={}", codeId, activationCode.getCode());
                return false;
            }

            if (activationCode.getStatus() == 2) {
                log.warn("激活码已被禁用: codeId={}, code={}", codeId, activationCode.getCode());
                return true; // 已经是禁用状态，返回成功
            }

            // 禁用激活码
            activationCode.setStatus(2); // 2表示已禁用
            activationCode.setUpdatedTime(LocalDateTime.now());

            boolean success = this.updateById(activationCode);

            if (success) {
                log.info("管理员{}成功禁用激活码: codeId={}, code={}", adminId, codeId, activationCode.getCode());
            } else {
                log.error("禁用激活码失败: codeId={}, adminId={}", codeId, adminId);
            }

            return success;

        } catch (Exception e) {
            log.error("禁用激活码异常: codeId={}, adminId={}", codeId, adminId, e);
            throw new RuntimeException("禁用激活码失败: " + e.getMessage());
        }
    }

    @Override
    public List<ActivationCodeDTO> getActivationCodeListWithUserInfo(Integer status, String codeType) {
        try {
            // 获取激活码列表
            List<ActivationCode> activationCodes = getActivationCodeList(status, codeType);

            // 转换为DTO并填充用户信息
            return activationCodes.stream().map(code -> {
                ActivationCodeDTO dto = ActivationCodeDTO.fromEntity(code);

                // 如果激活码已被使用，获取使用者信息
                if (code.getUsedBy() != null) {
                    User user = userService.getById(code.getUsedBy());
                    if (user != null) {
                        dto.setUserInfo(user.getUsername(), user.getNickname());
                    }
                }

                return dto;
            }).collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取激活码列表（含用户信息）失败", e);
            throw new RuntimeException("获取激活码列表失败: " + e.getMessage());
        }
    }
}
