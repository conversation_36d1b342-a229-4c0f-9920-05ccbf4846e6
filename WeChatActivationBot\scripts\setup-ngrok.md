# 使用ngrok进行内网穿透测试

## 安装ngrok
1. 访问 https://ngrok.com/
2. 注册账号并下载ngrok
3. 配置authtoken

## 启动内网穿透
```bash
# 启动微信机器人服务
node src/app.js

# 新开一个终端，启动ngrok
ngrok http 3001
```

## 获取公网URL
ngrok启动后会显示类似：
```
Forwarding  https://abc123.ngrok.io -> http://localhost:3001
```

## 微信公众号配置
在微信公众平台配置：
- URL: https://abc123.ngrok.io/wechat
- Token: wechat_activation_bot_2025
- EncodingAESKey: 随机生成
- 消息加解密方式: 明文模式

## 注意事项
- ngrok免费版URL会变化，每次重启都需要重新配置
- 仅用于测试，生产环境请使用固定域名
