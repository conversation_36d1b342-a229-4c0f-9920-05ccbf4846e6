// 本地存储工具类
class StorageUtil {
  // 保存数据
  static set(key, value) {
    try {
      uni.setStorageSync(key, JSON.stringify(value))
      return true
    } catch (error) {
      console.error('Storage set error:', error)
      return false
    }
  }
  
  // 获取数据
  static get(key, defaultValue = null) {
    try {
      const value = uni.getStorageSync(key)
      return value ? JSON.parse(value) : defaultValue
    } catch (error) {
      console.error('Storage get error:', error)
      return defaultValue
    }
  }
  
  // 删除数据
  static remove(key) {
    try {
      uni.removeStorageSync(key)
      return true
    } catch (error) {
      console.error('Storage remove error:', error)
      return false
    }
  }
  
  // 清空所有数据
  static clear() {
    try {
      uni.clearStorageSync()
      return true
    } catch (error) {
      console.error('Storage clear error:', error)
      return false
    }
  }
}

// 历史记录管理
class HistoryManager {
  static HISTORY_KEY = 'reply_history'
  static MAX_HISTORY = 100
  
  // 添加历史记录
  static addHistory(originalMessage, replyList, emotionResult) {
    const history = this.getHistory()
    const newRecord = {
      id: Date.now(),
      originalMessage,
      replyList,
      emotionResult,
      createTime: new Date().toISOString(),
      isFavorite: false
    }
    
    history.unshift(newRecord)
    
    // 限制历史记录数量
    if (history.length > this.MAX_HISTORY) {
      history.splice(this.MAX_HISTORY)
    }
    
    StorageUtil.set(this.HISTORY_KEY, history)
    return newRecord
  }
  
  // 获取历史记录
  static getHistory() {
    return StorageUtil.get(this.HISTORY_KEY, [])
  }
  
  // 删除历史记录
  static removeHistory(id) {
    const history = this.getHistory()
    const index = history.findIndex(item => item.id === id)
    if (index > -1) {
      history.splice(index, 1)
      StorageUtil.set(this.HISTORY_KEY, history)
      return true
    }
    return false
  }
  
  // 收藏/取消收藏
  static toggleFavorite(id) {
    const history = this.getHistory()
    const item = history.find(item => item.id === id)
    if (item) {
      item.isFavorite = !item.isFavorite
      StorageUtil.set(this.HISTORY_KEY, history)
      return item.isFavorite
    }
    return false
  }
  
  // 获取收藏列表
  static getFavorites() {
    return this.getHistory().filter(item => item.isFavorite)
  }
  
  // 清空历史记录
  static clearHistory() {
    StorageUtil.remove(this.HISTORY_KEY)
  }
}

// 用户设置管理
class SettingsManager {
  static SETTINGS_KEY = 'user_settings'
  
  static getDefaultSettings() {
    return {
      theme: 'light',
      fontSize: 'medium',
      autoSave: true,
      showFloatingBubble: false,
      replyStyles: ['warm_caring', 'humorous', 'rational', 'concise', 'romantic'],
      dailyQuota: 10,
      notifications: {
        enabled: true,
        sound: true,
        vibrate: true
      }
    }
  }
  
  // 获取设置
  static getSettings() {
    return StorageUtil.get(this.SETTINGS_KEY, this.getDefaultSettings())
  }
  
  // 更新设置
  static updateSettings(newSettings) {
    const currentSettings = this.getSettings()
    const updatedSettings = { ...currentSettings, ...newSettings }
    StorageUtil.set(this.SETTINGS_KEY, updatedSettings)
    return updatedSettings
  }
  
  // 重置设置
  static resetSettings() {
    const defaultSettings = this.getDefaultSettings()
    StorageUtil.set(this.SETTINGS_KEY, defaultSettings)
    return defaultSettings
  }
}

// 缓存管理工具类
class CacheManager {
  // 缓存类型定义
  static CACHE_TYPES = {
    USER_DATA: 'user_data',
    SETTINGS: 'settings',
    HISTORY: 'history',
    TEMP_DATA: 'temp_data',
    IMAGE_CACHE: 'image_cache',
    API_CACHE: 'api_cache'
  }

  // 需要保留的关键数据（清除缓存时不删除）
  static PROTECTED_KEYS = [
    'token',
    'user_info',
    'current_user_id'
  ]

  /**
   * 计算缓存大小
   * @returns {Promise<Object>} 缓存大小信息
   */
  static async calculateCacheSize() {
    try {
      let totalSize = 0
      const cacheDetails = {}

      // 快速计算本地存储大小
      const localStorageSize = this.calculateLocalStorageSize()
      totalSize += localStorageSize
      cacheDetails.localStorage = this.formatSize(localStorageSize)

      // 简化的估算，避免复杂计算
      // #ifdef APP-PLUS
      const imageCacheSize = 1024 * 1024 // 1MB估算
      const tempFileSize = 512 * 1024   // 512KB估算
      // #endif

      // #ifndef APP-PLUS
      const imageCacheSize = 0
      const tempFileSize = 0
      // #endif

      totalSize += imageCacheSize + tempFileSize
      cacheDetails.imageCache = this.formatSize(imageCacheSize)
      cacheDetails.tempFiles = this.formatSize(tempFileSize)

      // 快速获取存储项目数量
      let itemCount = 0
      try {
        const storageInfo = uni.getStorageInfoSync()
        itemCount = storageInfo.keys ? storageInfo.keys.length : 0
      } catch (error) {
        itemCount = 0
      }

      return {
        total: this.formatSize(totalSize),
        totalBytes: totalSize,
        details: cacheDetails,
        itemCount: itemCount
      }
    } catch (error) {
      console.error('计算缓存大小失败:', error)
      return {
        total: '未知',
        totalBytes: 0,
        details: {},
        itemCount: 0
      }
    }
  }

  /**
   * 获取存储信息（同步方式）
   */
  static getStorageInfo() {
    try {
      const storageInfo = uni.getStorageInfoSync()
      return {
        keys: storageInfo.keys || [],
        length: storageInfo.keys ? storageInfo.keys.length : 0
      }
    } catch (error) {
      console.error('获取存储信息失败:', error)
      return { keys: [], length: 0 }
    }
  }

  /**
   * 计算本地存储大小
   */
  static calculateLocalStorageSize() {
    try {
      const storageInfo = uni.getStorageInfoSync()
      // 估算：每个字符约1字节，加上key的长度
      let size = 0

      if (storageInfo.keys) {
        storageInfo.keys.forEach(key => {
          try {
            const value = uni.getStorageSync(key)
            const valueStr = typeof value === 'string' ? value : JSON.stringify(value)
            size += key.length + valueStr.length
          } catch (error) {
            // 忽略单个key的错误
          }
        })
      }

      return size
    } catch (error) {
      return 0
    }
  }

  /**
   * 计算图片缓存大小（估算）
   */
  static async calculateImageCacheSize() {
    // 简化估算，避免复杂计算
    return 1024 * 1024 // 默认1MB
  }

  /**
   * 计算临时文件大小
   */
  static async calculateTempFileSize() {
    // 临时文件大小估算
    return 512 * 1024 // 默认512KB
  }

  /**
   * 格式化文件大小
   */
  static formatSize(bytes) {
    if (bytes === 0) return '0B'

    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + sizes[i]
  }

  /**
   * 清除缓存
   * @param {Object} options 清除选项
   */
  static async clearCache(options = {}) {
    const {
      clearUserData = false,
      clearSettings = false,
      clearHistory = true,
      clearTempData = true,
      clearImageCache = true,
      clearApiCache = true
    } = options

    const results = {
      success: true,
      clearedItems: [],
      errors: [],
      freedSpace: 0
    }

    try {
      // 获取清除前的大小
      const beforeSize = await this.calculateCacheSize()

      // 获取所有存储的key
      const storageInfo = this.getStorageInfo()

      for (const key of storageInfo.keys) {
        try {
          // 检查是否为受保护的key
          if (this.PROTECTED_KEYS.includes(key)) {
            continue
          }

          let shouldClear = false

          // 根据选项决定是否清除
          if (clearUserData && this.isUserDataKey(key)) {
            shouldClear = true
          } else if (clearSettings && this.isSettingsKey(key)) {
            shouldClear = true
          } else if (clearHistory && this.isHistoryKey(key)) {
            shouldClear = true
          } else if (clearTempData && this.isTempDataKey(key)) {
            shouldClear = true
          } else if (clearApiCache && this.isApiCacheKey(key)) {
            shouldClear = true
          }

          if (shouldClear) {
            uni.removeStorageSync(key)
            results.clearedItems.push(key)
          }
        } catch (error) {
          results.errors.push(`清除${key}失败: ${error.message}`)
        }
      }

      // 清除图片缓存（异步但不等待）
      if (clearImageCache) {
        this.clearImageCache().catch(error => {
          console.warn('清除图片缓存失败:', error)
        })
        results.clearedItems.push('图片缓存')
      }

      // 清除临时文件（异步但不等待）
      if (clearTempData) {
        this.clearTempFiles().catch(error => {
          console.warn('清除临时文件失败:', error)
        })
        results.clearedItems.push('临时文件')
      }

      // 计算释放的空间
      const afterSize = await this.calculateCacheSize()
      results.freedSpace = beforeSize.totalBytes - afterSize.totalBytes

    } catch (error) {
      results.success = false
      results.errors.push(`清除缓存失败: ${error.message}`)
    }

    return results
  }

  /**
   * 判断key类型的辅助方法
   */
  static isUserDataKey(key) {
    return key.includes('user_') || key.includes('profile_')
  }

  static isSettingsKey(key) {
    return key.includes('settings') || key.includes('config')
  }

  static isHistoryKey(key) {
    return key.includes('history') || key.includes('record')
  }

  static isTempDataKey(key) {
    return key.includes('temp_') || key.includes('cache_') || key.includes('tmp_')
  }

  static isApiCacheKey(key) {
    return key.includes('api_') || key.includes('request_')
  }

  /**
   * 清除图片缓存
   */
  static async clearImageCache() {
    return new Promise((resolve) => {
      // #ifdef APP-PLUS
      try {
        // App端清除图片缓存
        plus.cache.clear(() => {
          resolve()
        })
      } catch (error) {
        console.error('清除图片缓存失败:', error)
        resolve() // 即使失败也要resolve，避免阻塞
      }
      // #endif

      // #ifndef APP-PLUS
      // 非App端直接resolve
      resolve()
      // #endif
    })
  }

  /**
   * 清除临时文件
   */
  static async clearTempFiles() {
    return new Promise((resolve) => {
      // #ifdef APP-PLUS
      try {
        // App端清除临时文件
        const tempDir = plus.io.convertLocalFileSystemURL('_doc/temp/')
        plus.io.resolveLocalFileSystemURL(tempDir, (entry) => {
          entry.removeRecursively(() => {
            resolve()
          }, (error) => {
            console.error('清除临时文件失败:', error)
            resolve() // 即使失败也要resolve
          })
        }, (error) => {
          console.error('临时目录不存在或无法访问:', error)
          resolve() // 目录不存在也算成功
        })
      } catch (error) {
        console.error('清除临时文件失败:', error)
        resolve()
      }
      // #endif

      // #ifndef APP-PLUS
      // 非App端直接resolve
      resolve()
      // #endif
    })
  }

  /**
   * 获取缓存统计信息
   */
  static async getCacheStats() {
    const cacheSize = await this.calculateCacheSize()
    const storageInfo = await this.getStorageInfo()

    return {
      totalSize: cacheSize.total,
      totalBytes: cacheSize.totalBytes,
      itemCount: storageInfo.length,
      details: cacheSize.details,
      lastCalculated: new Date().toISOString()
    }
  }

  /**
   * 简化的清除缓存方法（备用方案）
   * 只清除本地存储，不涉及文件系统操作
   */
  static async clearCacheSimple(options = {}) {
    const {
      clearUserData = false,
      clearSettings = false,
      clearHistory = true,
      clearTempData = true,
      clearApiCache = true
    } = options

    const results = {
      success: true,
      clearedItems: [],
      errors: [],
      freedSpace: 0
    }

    try {
      const beforeSize = await this.calculateCacheSize()
      const storageInfo = await this.getStorageInfo()

      for (const key of storageInfo.keys) {
        try {
          // 检查是否为受保护的key
          if (this.PROTECTED_KEYS.includes(key)) {
            continue
          }

          let shouldClear = false

          // 根据选项决定是否清除
          if (clearUserData && this.isUserDataKey(key)) {
            shouldClear = true
          } else if (clearSettings && this.isSettingsKey(key)) {
            shouldClear = true
          } else if (clearHistory && this.isHistoryKey(key)) {
            shouldClear = true
          } else if (clearTempData && this.isTempDataKey(key)) {
            shouldClear = true
          } else if (clearApiCache && this.isApiCacheKey(key)) {
            shouldClear = true
          }

          if (shouldClear) {
            uni.removeStorageSync(key)
            results.clearedItems.push(key)
          }
        } catch (error) {
          results.errors.push(`清除${key}失败: ${error.message}`)
        }
      }

      // 计算释放的空间
      const afterSize = await this.calculateCacheSize()
      results.freedSpace = beforeSize.totalBytes - afterSize.totalBytes

    } catch (error) {
      results.success = false
      results.errors.push(`清除缓存失败: ${error.message}`)
    }

    return results
  }
}

export {
  StorageUtil,
  HistoryManager,
  SettingsManager,
  CacheManager
}
