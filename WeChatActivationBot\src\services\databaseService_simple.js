const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const Logger = require('../utils/logger');

class DatabaseService {
    constructor() {
        this.dbPath = process.env.DB_PATH || './data/activation_bot.db';
        this.db = null;
    }

    /**
     * 初始化数据库
     */
    initialize() {
        try {
            // 确保数据目录存在
            const dbDir = path.dirname(this.dbPath);
            if (!fs.existsSync(dbDir)) {
                fs.mkdirSync(dbDir, { recursive: true });
            }

            // 连接数据库
            this.db = new Database(this.dbPath);
            
            // 创建表
            this.createTables();
            
            Logger.info('数据库初始化完成');
        } catch (error) {
            Logger.error('数据库初始化失败:', error);
            throw error;
        }
    }

    /**
     * 创建数据库表
     */
    createTables() {
        const tables = [
            `CREATE TABLE IF NOT EXISTS keywords (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                keyword TEXT UNIQUE NOT NULL,
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,
            
            `CREATE TABLE IF NOT EXISTS activation_codes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                keyword_id INTEGER NOT NULL,
                code TEXT UNIQUE NOT NULL,
                used_by TEXT,
                used_at DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (keyword_id) REFERENCES keywords (id)
            )`
        ];

        for (const sql of tables) {
            this.db.exec(sql);
        }

        // 创建索引
        this.db.exec('CREATE INDEX IF NOT EXISTS idx_keywords_keyword ON keywords(keyword)');
        this.db.exec('CREATE INDEX IF NOT EXISTS idx_activation_codes_keyword_id ON activation_codes(keyword_id)');
        this.db.exec('CREATE INDEX IF NOT EXISTS idx_activation_codes_code ON activation_codes(code)');
    }

    // ==================== 关键字相关方法 ====================

    getAllKeywords() {
        const stmt = this.db.prepare('SELECT * FROM keywords ORDER BY created_at DESC');
        return stmt.all();
    }

    getKeywordById(id) {
        const stmt = this.db.prepare('SELECT * FROM keywords WHERE id = ?');
        return stmt.get(id);
    }

    addKeyword(keyword, description = '') {
        const stmt = this.db.prepare('INSERT INTO keywords (keyword, description) VALUES (?, ?)');
        const result = stmt.run(keyword, description);
        return this.getKeywordById(result.lastInsertRowid);
    }

    removeKeyword(id) {
        const stmt = this.db.prepare('DELETE FROM keywords WHERE id = ?');
        return stmt.run(id);
    }

    // ==================== 激活码相关方法 ====================

    getAvailableActivationCode(keywordId) {
        const stmt = this.db.prepare(`
            SELECT * FROM activation_codes 
            WHERE keyword_id = ? AND used_by IS NULL 
            ORDER BY created_at ASC 
            LIMIT 1
        `);
        return stmt.get(keywordId);
    }

    assignActivationCode(codeId, userId) {
        const stmt = this.db.prepare('UPDATE activation_codes SET used_by = ?, used_at = CURRENT_TIMESTAMP WHERE id = ?');
        return stmt.run(userId, codeId);
    }

    getUserActivationCode(keywordId, userId) {
        const stmt = this.db.prepare(`
            SELECT * FROM activation_codes 
            WHERE keyword_id = ? AND used_by = ?
        `);
        return stmt.get(keywordId, userId);
    }

    batchInsertActivationCodes(keywordId, codes) {
        const stmt = this.db.prepare('INSERT INTO activation_codes (keyword_id, code) VALUES (?, ?)');
        const results = [];
        
        for (const code of codes) {
            const result = stmt.run(keywordId, code);
            results.push({ id: result.lastInsertRowid, code, keyword_id: keywordId });
        }
        
        return results;
    }

    getActivationCodesByKeyword(keywordId) {
        const stmt = this.db.prepare('SELECT * FROM activation_codes WHERE keyword_id = ? ORDER BY created_at DESC');
        return stmt.all(keywordId);
    }

    getActivationCodeByCode(code) {
        const stmt = this.db.prepare('SELECT * FROM activation_codes WHERE code = ?');
        return stmt.get(code);
    }

    addActivationCode(keywordId, code) {
        const stmt = this.db.prepare('INSERT INTO activation_codes (keyword_id, code) VALUES (?, ?)');
        const result = stmt.run(keywordId, code);
        return { id: result.lastInsertRowid, keyword_id: keywordId, code };
    }

    removeActivationCode(id) {
        const stmt = this.db.prepare('DELETE FROM activation_codes WHERE id = ?');
        return stmt.run(id);
    }

    getActivationCodeStats(keywordId) {
        const stmt = this.db.prepare(`
            SELECT 
                COUNT(*) as total,
                COUNT(used_by) as used
            FROM activation_codes 
            WHERE keyword_id = ?
        `);
        return stmt.get(keywordId);
    }

    resetActivationCodes(keywordId) {
        const stmt = this.db.prepare('UPDATE activation_codes SET used_by = NULL, used_at = NULL WHERE keyword_id = ?');
        const result = stmt.run(keywordId);
        return result.changes;
    }

    /**
     * 关闭数据库连接
     */
    close() {
        if (this.db) {
            this.db.close();
            Logger.info('数据库连接已关闭');
        }
    }
}

module.exports = DatabaseService;
