#!/usr/bin/env node

const bcrypt = require('bcryptjs');
const readline = require('readline');

/**
 * 密码哈希生成工具
 * 用于生成安全的密码哈希，避免在配置文件中存储明文密码
 */

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function hideInput() {
    // 隐藏输入的密码
    const stdin = process.stdin;
    stdin.setRawMode(true);
    stdin.resume();
    stdin.setEncoding('utf8');
    
    let password = '';
    
    return new Promise((resolve) => {
        stdin.on('data', function(char) {
            char = char + '';
            
            switch (char) {
                case '\n':
                case '\r':
                case '\u0004':
                    // 回车键，结束输入
                    stdin.setRawMode(false);
                    stdin.pause();
                    console.log(''); // 换行
                    resolve(password);
                    break;
                case '\u0003':
                    // Ctrl+C，退出
                    console.log('\n操作已取消');
                    process.exit();
                    break;
                case '\u007f':
                    // 退格键
                    if (password.length > 0) {
                        password = password.slice(0, -1);
                        process.stdout.write('\b \b');
                    }
                    break;
                default:
                    // 普通字符
                    password += char;
                    process.stdout.write('*');
                    break;
            }
        });
    });
}

async function generatePasswordHash() {
    console.log('🔐 微信激活码机器人 - 密码哈希生成工具');
    console.log('');
    console.log('此工具用于生成安全的密码哈希，避免在配置文件中存储明文密码。');
    console.log('');
    
    try {
        // 获取用户名
        const username = await new Promise((resolve) => {
            rl.question('请输入管理员用户名 (默认: admin): ', (answer) => {
                resolve(answer.trim() || 'admin');
            });
        });
        
        // 获取密码
        console.log('请输入管理员密码 (输入时不会显示): ');
        const password = await hideInput();
        
        if (!password || password.length < 6) {
            console.log('❌ 密码长度至少需要6个字符');
            process.exit(1);
        }
        
        // 确认密码
        console.log('请再次输入密码确认: ');
        const confirmPassword = await hideInput();
        
        if (password !== confirmPassword) {
            console.log('❌ 两次输入的密码不一致');
            process.exit(1);
        }
        
        // 生成哈希
        console.log('正在生成密码哈希...');
        const saltRounds = 12; // 更高的安全级别
        const hash = await bcrypt.hash(password, saltRounds);
        
        console.log('');
        console.log('✅ 密码哈希生成成功！');
        console.log('');
        console.log('请将以下配置添加到您的 .env 文件中：');
        console.log('');
        console.log('# 管理员登录配置（安全版本）');
        console.log(`ADMIN_USERNAME=${username}`);
        console.log(`ADMIN_PASSWORD_HASH=${hash}`);
        console.log('');
        console.log('⚠️  重要提醒：');
        console.log('1. 请删除 .env 文件中的 ADMIN_PASSWORD 配置（如果存在）');
        console.log('2. 请确保 .env 文件不会被提交到版本控制系统');
        console.log('3. 请妥善保管您的密码，哈希值无法逆向解密');
        console.log('');
        
    } catch (error) {
        console.error('❌ 生成密码哈希失败:', error.message);
        process.exit(1);
    } finally {
        rl.close();
    }
}

// 命令行参数处理
if (process.argv.length > 2) {
    const command = process.argv[2];
    
    if (command === '--help' || command === '-h') {
        console.log('🔐 密码哈希生成工具');
        console.log('');
        console.log('使用方法:');
        console.log('  node scripts/generate-password-hash.js');
        console.log('');
        console.log('选项:');
        console.log('  --help, -h     显示帮助信息');
        console.log('  --version, -v  显示版本信息');
        process.exit(0);
    }
    
    if (command === '--version' || command === '-v') {
        console.log('密码哈希生成工具 v1.0.0');
        process.exit(0);
    }
}

// 主函数
if (require.main === module) {
    generatePasswordHash().catch(error => {
        console.error('程序执行失败:', error);
        process.exit(1);
    });
}

module.exports = { generatePasswordHash };
