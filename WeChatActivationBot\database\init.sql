-- 微信自动回复机器人数据库初始化脚本
-- 创建时间: 2025-07-08

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `wechat_reply_bot` 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `wechat_reply_bot`;

-- 用户表 - 记录关注用户和激活码使用情况
CREATE TABLE IF NOT EXISTS `wechat_users` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `openid` VARCHAR(64) UNIQUE NOT NULL COMMENT '微信用户openid',
    `nickname` VARCHAR(255) DEFAULT NULL COMMENT '用户昵称',
    `is_subscribed` TINYINT DEFAULT 1 COMMENT '是否关注：1-关注，0-未关注',
    `subscribe_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '关注时间',
    `unsubscribe_time` DATETIME DEFAULT NULL COMMENT '取消关注时间',
    `activation_code_used` VARCHAR(32) DEFAULT NULL COMMENT '已使用的激活码',
    `activation_code_time` DATETIME DEFAULT NULL COMMENT '激活码使用时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_openid` (`openid`),
    INDEX `idx_subscribed` (`is_subscribed`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信用户表';

-- 关键词表 - 动态配置关键词和回复内容
CREATE TABLE IF NOT EXISTS `reply_keywords` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `keyword` VARCHAR(255) NOT NULL COMMENT '关键词',
    `reply_type` ENUM('activation_code', 'text') DEFAULT 'text' COMMENT '回复类型',
    `reply_content` TEXT COMMENT '回复内容模板',
    `activation_code_type` VARCHAR(20) DEFAULT NULL COMMENT '激活码类型：vip_1d等',
    `is_active` TINYINT DEFAULT 1 COMMENT '是否启用',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_keyword` (`keyword`),
    INDEX `idx_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='回复关键词表';

-- 插入默认关键词
INSERT INTO `reply_keywords` (`keyword`, `reply_type`, `reply_content`, `activation_code_type`)
VALUES ('emo', 'activation_code', 'congratulations! Your 1-day VIP activation code: {code}\n\nPlease keep it safe, the activation code can only be used once!', 'vip_1d')
ON DUPLICATE KEY UPDATE
    `reply_content` = VALUES(`reply_content`),
    `activation_code_type` = VALUES(`activation_code_type`),
    `updated_at` = CURRENT_TIMESTAMP;