#!/usr/bin/env node

const crypto = require('crypto');
require('dotenv').config();

/**
 * 微信公众号配置验证工具
 */

function verifyWeChatSignature(signature, timestamp, nonce, token) {
    const tmpArr = [token, timestamp, nonce].sort();
    const tmpStr = tmpArr.join('');
    const sha1 = crypto.createHash('sha1');
    sha1.update(tmpStr);
    const result = sha1.digest('hex');
    return result === signature;
}

function testWeChatConfig() {
    console.log('🔧 微信公众号配置验证\n');
    
    const config = {
        token: process.env.WECHAT_TOKEN,
        appid: process.env.WECHAT_APPID,
        appsecret: process.env.WECHAT_APPSECRET,
        port: process.env.PORT || 3001
    };
    
    console.log('📋 当前配置:');
    console.log(`   Token: ${config.token}`);
    console.log(`   AppID: ${config.appid}`);
    console.log(`   AppSecret: ${config.appsecret ? '已配置' : '未配置'}`);
    console.log(`   端口: ${config.port}\n`);
    
    // 验证配置完整性
    if (!config.token || !config.appid || !config.appsecret) {
        console.log('❌ 配置不完整，请检查.env文件');
        return false;
    }
    
    console.log('✅ 配置验证通过\n');
    
    // 模拟微信验证请求
    console.log('🧪 模拟微信验证请求:');
    const testData = {
        signature: 'test_signature',
        timestamp: Math.floor(Date.now() / 1000).toString(),
        nonce: 'test_nonce',
        echostr: 'test_echo'
    };
    
    console.log('   验证参数:', testData);
    
    // 生成正确的签名
    const correctSignature = crypto.createHash('sha1')
        .update([config.token, testData.timestamp, testData.nonce].sort().join(''))
        .digest('hex');
    
    console.log(`   正确签名: ${correctSignature}`);
    
    // 验证签名
    const isValid = verifyWeChatSignature(correctSignature, testData.timestamp, testData.nonce, config.token);
    console.log(`   签名验证: ${isValid ? '✅ 通过' : '❌ 失败'}\n`);
    
    // 显示配置URL
    console.log('🌐 微信公众号配置信息:');
    console.log('   在微信公众平台配置以下信息:');
    console.log(`   URL: https://your-domain.com/wechat`);
    console.log(`   Token: ${config.token}`);
    console.log('   EncodingAESKey: 随机生成43位字符');
    console.log('   消息加解密方式: 明文模式\n');
    
    console.log('📝 测试步骤:');
    console.log('1. 启动服务: node src/app.js');
    console.log('2. 使用ngrok或部署到服务器获取公网URL');
    console.log('3. 在微信公众平台配置服务器URL');
    console.log('4. 点击"提交"进行验证');
    console.log('5. 验证通过后即可接收微信消息\n');
    
    return true;
}

// 生成测试用户数据
function generateTestUsers() {
    console.log('👥 生成测试用户数据:\n');
    
    const testUsers = [
        {
            openid: 'oTest001_' + Date.now(),
            nickname: '测试用户A',
            action: '关注公众号'
        },
        {
            openid: 'oTest002_' + Date.now(),
            nickname: '测试用户B', 
            action: '发送"emo"获取激活码'
        },
        {
            openid: 'oTest003_' + Date.now(),
            nickname: '测试用户C',
            action: '重复发送"emo"测试防重复'
        }
    ];
    
    testUsers.forEach((user, index) => {
        console.log(`${index + 1}. ${user.nickname} (${user.openid})`);
        console.log(`   测试场景: ${user.action}\n`);
    });
    
    return testUsers;
}

// 主函数
function main() {
    const args = process.argv.slice(2);
    
    if (args.includes('--help') || args.includes('-h')) {
        console.log('🔧 微信公众号配置验证工具');
        console.log('');
        console.log('使用方法:');
        console.log('  node scripts/test-wechat-config.js        # 验证配置');
        console.log('  node scripts/test-wechat-config.js users  # 生成测试用户');
        console.log('');
        return;
    }
    
    if (args.includes('users')) {
        generateTestUsers();
    } else {
        testWeChatConfig();
    }
}

if (require.main === module) {
    main();
}

module.exports = { verifyWeChatSignature, testWeChatConfig, generateTestUsers };
