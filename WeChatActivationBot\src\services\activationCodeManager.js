const crypto = require('crypto');
const Logger = require('../utils/logger');

class ActivationCodeManager {
    constructor(databaseService) {
        this.db = databaseService;
        this.codeLength = parseInt(process.env.DEFAULT_ACTIVATION_CODE_LENGTH) || 16;
    }

    /**
     * 获取激活码（为用户分配）
     */
    async getActivationCode(keywordId, userId) {
        try {
            // 检查用户是否已经获取过该关键字的激活码
            const existingCode = await this.db.getUserActivationCode(keywordId, userId);
            if (existingCode) {
                Logger.info(`用户 ${userId} 已获取过关键字 ${keywordId} 的激活码`);
                return existingCode;
            }

            // 获取可用的激活码
            const availableCode = await this.db.getAvailableActivationCode(keywordId);
            if (!availableCode) {
                Logger.warn(`关键字 ${keywordId} 没有可用的激活码`);
                return null;
            }

            // 分配激活码给用户
            await this.db.assignActivationCode(availableCode.id, userId);
            
            Logger.info(`为用户 ${userId} 分配激活码: ${availableCode.code}`);
            return availableCode;

        } catch (error) {
            Logger.error('获取激活码失败:', error);
            throw error;
        }
    }

    /**
     * 批量生成激活码
     */
    async generateActivationCodes(keywordId, count, codeLength = null) {
        try {
            const length = codeLength || this.codeLength;
            const codes = [];
            const existingCodes = new Set();

            // 获取已存在的激活码以避免重复
            const existing = await this.db.getActivationCodesByKeyword(keywordId);
            existing.forEach(code => existingCodes.add(code.code));

            // 生成指定数量的唯一激活码
            while (codes.length < count) {
                const code = this.generateRandomCode(length);
                if (!existingCodes.has(code)) {
                    codes.push(code);
                    existingCodes.add(code);
                }
            }

            // 批量插入数据库
            const insertedCodes = await this.db.batchInsertActivationCodes(keywordId, codes);
            
            Logger.info(`为关键字 ${keywordId} 生成了 ${codes.length} 个激活码`);
            return insertedCodes;

        } catch (error) {
            Logger.error('生成激活码失败:', error);
            throw error;
        }
    }

    /**
     * 生成随机激活码
     */
    generateRandomCode(length) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        
        for (let i = 0; i < length; i++) {
            const randomIndex = crypto.randomInt(0, chars.length);
            result += chars[randomIndex];
        }
        
        return result;
    }

    /**
     * 验证激活码格式
     */
    validateCodeFormat(code) {
        const pattern = /^[A-Z0-9]+$/;
        return pattern.test(code) && code.length >= 8 && code.length <= 32;
    }

    /**
     * 手动添加激活码
     */
    async addActivationCode(keywordId, code) {
        try {
            if (!this.validateCodeFormat(code)) {
                throw new Error('激活码格式无效');
            }

            // 检查激活码是否已存在
            const existing = await this.db.getActivationCodeByCode(code);
            if (existing) {
                throw new Error('激活码已存在');
            }

            const activationCode = await this.db.addActivationCode(keywordId, code);
            Logger.info(`手动添加激活码: ${code}`);
            return activationCode;

        } catch (error) {
            Logger.error('添加激活码失败:', error);
            throw error;
        }
    }

    /**
     * 删除激活码
     */
    async removeActivationCode(codeId) {
        try {
            await this.db.removeActivationCode(codeId);
            Logger.info(`删除激活码: ${codeId}`);
        } catch (error) {
            Logger.error('删除激活码失败:', error);
            throw error;
        }
    }

    /**
     * 获取关键字的激活码统计信息
     */
    async getActivationCodeStats(keywordId) {
        try {
            const stats = await this.db.getActivationCodeStats(keywordId);
            return {
                total: stats.total || 0,
                used: stats.used || 0,
                available: (stats.total || 0) - (stats.used || 0)
            };
        } catch (error) {
            Logger.error('获取激活码统计失败:', error);
            throw error;
        }
    }

    /**
     * 重置激活码（将已使用的激活码重置为可用）
     */
    async resetActivationCodes(keywordId) {
        try {
            const resetCount = await this.db.resetActivationCodes(keywordId);
            Logger.info(`重置关键字 ${keywordId} 的 ${resetCount} 个激活码`);
            return resetCount;
        } catch (error) {
            Logger.error('重置激活码失败:', error);
            throw error;
        }
    }

    /**
     * 导入激活码（从文件或数组）
     */
    async importActivationCodes(keywordId, codes) {
        try {
            const validCodes = codes.filter(code => this.validateCodeFormat(code));
            const invalidCodes = codes.filter(code => !this.validateCodeFormat(code));

            if (invalidCodes.length > 0) {
                Logger.warn(`发现 ${invalidCodes.length} 个格式无效的激活码`);
            }

            if (validCodes.length === 0) {
                throw new Error('没有有效的激活码可导入');
            }

            // 检查重复
            const existingCodes = await this.db.getActivationCodesByKeyword(keywordId);
            const existingCodeSet = new Set(existingCodes.map(c => c.code));
            
            const newCodes = validCodes.filter(code => !existingCodeSet.has(code));
            const duplicateCodes = validCodes.filter(code => existingCodeSet.has(code));

            if (duplicateCodes.length > 0) {
                Logger.warn(`发现 ${duplicateCodes.length} 个重复的激活码`);
            }

            if (newCodes.length === 0) {
                throw new Error('没有新的激活码可导入');
            }

            const importedCodes = await this.db.batchInsertActivationCodes(keywordId, newCodes);
            
            Logger.info(`成功导入 ${newCodes.length} 个激活码`);
            return {
                imported: newCodes.length,
                duplicates: duplicateCodes.length,
                invalid: invalidCodes.length,
                codes: importedCodes
            };

        } catch (error) {
            Logger.error('导入激活码失败:', error);
            throw error;
        }
    }

    /**
     * 导出激活码
     */
    async exportActivationCodes(keywordId, includeUsed = false) {
        try {
            const codes = await this.db.getActivationCodesByKeyword(keywordId);
            
            if (!includeUsed) {
                return codes.filter(code => !code.used_by && !code.used_at);
            }
            
            return codes;
        } catch (error) {
            Logger.error('导出激活码失败:', error);
            throw error;
        }
    }
}

module.exports = ActivationCodeManager;
