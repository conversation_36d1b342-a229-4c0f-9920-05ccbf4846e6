package com.emotional.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.emotional.service.entity.User;
import com.emotional.service.entity.UserStats;
import com.emotional.service.mapper.UserStatsMapper;
import com.emotional.service.service.UserService;
import com.emotional.service.service.UserStatsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户统计服务实现类
 */
@Slf4j
@Service
public class UserStatsServiceImpl extends ServiceImpl<UserStatsMapper, UserStats> implements UserStatsService {

    @Autowired
    private UserStatsMapper userStatsMapper;

    @Autowired
    private UserService userService;

    @Override
    public UserStats getUserStats(Long userId) {
        return getUserStatsByDate(userId, LocalDate.now());
    }

    @Override
    public UserStats getUserStatsByDate(Long userId, LocalDate date) {
        UserStats stats = userStatsMapper.getUserStatsByDate(userId, date);
        if (stats == null) {
            // 如果没有今日统计，创建一个
            stats = initTodayStats(userId);
        }
        return stats;
    }

    @Override
    @Transactional
    public boolean incrementUsage(Long userId) {
        try {
            LocalDate today = LocalDate.now();
            
            // 确保今日统计记录存在
            UserStats todayStats = getUserStatsByDate(userId, today);
            
            // 增加使用次数
            int updated = userStatsMapper.incrementDailyUsage(userId, today);
            
            if (updated > 0) {
                log.info("用户使用次数增加成功: userId={}, date={}", userId, today);
                return true;
            } else {
                log.warn("用户使用次数增加失败: userId={}, date={}", userId, today);
                return false;
            }
        } catch (Exception e) {
            log.error("增加用户使用次数异常: userId={}", userId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public UserStats initTodayStats(Long userId) {
        try {
            LocalDate today = LocalDate.now();
            
            // 检查是否已存在今日统计
            UserStats existingStats = userStatsMapper.getUserStatsByDate(userId, today);
            if (existingStats != null) {
                return existingStats;
            }
            
            // 获取用户信息
            User user = userService.getUserById(userId);
            if (user == null) {
                throw new RuntimeException("用户不存在: " + userId);
            }
            
            // 创建今日统计记录
            UserStats stats = new UserStats();
            stats.setUserId(userId);
            stats.setStatDate(today);
            stats.setDailyUsage(0);
            stats.setTotalUsage(getTotalUsage(userId));
            // 处理VIP状态和管理员权限
            Integer isVip = user.getIsVip();
            Integer isAdmin = user.getIsAdmin();
            boolean isVipUser = isVip != null && isVip == 1;
            boolean isAdminUser = isAdmin != null && isAdmin == 1;

            // 设置配额：管理员无限制，VIP用户50次，普通用户3次
            if (isAdminUser) {
                stats.setDailyQuota(-1); // -1表示无限制
            } else if (isVipUser) {
                stats.setDailyQuota(50);
            } else {
                stats.setDailyQuota(3);
            }
            stats.setIsVip(isVip);
            stats.setCreateTime(LocalDateTime.now());
            stats.setUpdateTime(LocalDateTime.now());
            
            // 保存到数据库
            save(stats);
            
            log.info("初始化用户今日统计成功: userId={}, date={}", userId, today);
            return stats;
            
        } catch (Exception e) {
            log.error("初始化用户今日统计失败: userId={}", userId, e);
            throw new RuntimeException("初始化统计失败", e);
        }
    }

    @Override
    public Integer getTotalUsage(Long userId) {
        try {
            Integer total = userStatsMapper.getTotalUsageByUserId(userId);
            return total != null ? total : 0;
        } catch (Exception e) {
            log.error("获取用户总使用次数失败: userId={}", userId, e);
            return 0;
        }
    }

    @Override
    public Integer getTodayUsage(Long userId) {
        try {
            LocalDate today = LocalDate.now();
            Integer usage = userStatsMapper.getDailyUsage(userId, today);
            return usage != null ? usage : 0;
        } catch (Exception e) {
            log.error("获取用户今日使用次数失败: userId={}", userId, e);
            return 0;
        }
    }

    @Override
    public boolean hasQuota(Long userId) {
        try {
            UserStats stats = getUserStats(userId);
            if (stats == null) {
                return true; // 如果没有统计记录，允许使用
            }

            // 管理员无限制配额
            if (stats.getDailyQuota() == -1) {
                return true;
            }

            return stats.getDailyUsage() < stats.getDailyQuota();
        } catch (Exception e) {
            log.error("检查用户配额失败: userId={}", userId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean updateUserQuotaAfterVipActivation(Long userId) {
        try {
            log.info("VIP激活后更新用户配额: userId={}", userId);

            // 获取用户信息
            User user = userService.getUserById(userId);
            if (user == null) {
                log.error("用户不存在: userId={}", userId);
                return false;
            }

            // 更新今日统计的配额
            LocalDate today = LocalDate.now();
            UserStats todayStats = getUserStatsByDate(userId, today);

            if (todayStats != null) {
                // 更新为VIP配额
                todayStats.setDailyQuota(50);
                todayStats.setIsVip(1);
                todayStats.setUpdateTime(LocalDateTime.now());
                updateById(todayStats);

                log.info("今日配额已更新为VIP配额: userId={}, quota=50", userId);
            }

            // 更新用户表中的配额
            int updated = userStatsMapper.updateUserQuotaByUserId(userId, 50);

            if (updated > 0) {
                log.info("用户配额更新成功: userId={}, newQuota=50", userId);
                return true;
            } else {
                log.warn("用户配额更新失败: userId={}", userId);
                return false;
            }

        } catch (Exception e) {
            log.error("VIP激活后更新配额失败: userId={}", userId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean updateUserQuotaAfterVipExpired(Long userId) {
        try {
            log.info("VIP过期后更新用户配额: userId={}", userId);

            // 获取用户信息
            User user = userService.getUserById(userId);
            if (user == null) {
                log.error("用户不存在: userId={}", userId);
                return false;
            }

            // 更新今日统计的配额
            LocalDate today = LocalDate.now();
            UserStats todayStats = getUserStatsByDate(userId, today);

            if (todayStats != null) {
                // 更新为普通用户配额
                todayStats.setDailyQuota(3);
                todayStats.setIsVip(0);
                todayStats.setUpdateTime(LocalDateTime.now());
                updateById(todayStats);

                log.info("今日配额已降级为普通用户配额: userId={}, quota=3", userId);
            }

            // 更新用户统计表中的配额
            int updated = userStatsMapper.updateUserQuotaAfterExpired(userId, 3);

            if (updated > 0) {
                log.info("用户配额降级成功: userId={}, newQuota=3", userId);
                return true;
            } else {
                log.warn("用户配额降级失败: userId={}", userId);
                return false;
            }

        } catch (Exception e) {
            log.error("VIP过期后更新配额失败: userId={}", userId, e);
            return false;
        }
    }

    @Override
    public boolean batchUpdateExpiredVipUsersStats() {
        try {
            log.info("开始批量更新过期VIP用户统计表配额");
            int affectedRows = userStatsMapper.batchUpdateExpiredVipUsersStats();
            log.info("批量更新统计表完成，影响{}条记录", affectedRows);
            return true;
        } catch (Exception e) {
            log.error("批量更新过期VIP用户统计表失败", e);
            return false;
        }
    }
}
