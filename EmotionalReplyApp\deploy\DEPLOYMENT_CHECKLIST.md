# 🚀 情感回复助手APP后端部署检查清单

## 📋 部署前必检项目

### ✅ **环境准备**
- [ ] 服务器配置：2核2G以上
- [ ] 操作系统：Ubuntu 20.04+ / CentOS 7+
- [ ] Docker & Docker Compose 已安装
- [ ] 网络端口：8080（后端API）、3306（MySQL）、6379（Redis）

### ✅ **配置文件检查**
- [ ] 复制 `.env.example` 为 `.env`
- [ ] 填写数据库密码 `DB_PASSWORD`
- [ ] 填写MySQL root密码 `MYSQL_ROOT_PASSWORD`
- [ ] 生成并填写JWT密钥 `JWT_SECRET`（至少32位）
- [ ] 设置Grafana管理员密码 `GRAFANA_PASSWORD`
- [ ] 配置第三方API密钥（如有）

### ✅ **安全配置**
- [ ] 更改默认管理员密码
- [ ] 配置防火墙规则（开放8080端口）
- [ ] 配置备份策略
- [ ] 设置监控告警

### ✅ **数据库准备**
- [ ] 数据库初始化脚本已准备
- [ ] 数据库连接配置正确
- [ ] 数据库用户权限配置正确

## 🔧 **部署步骤**

### 1. **克隆项目**
```bash
git clone <repository-url>
cd EmotionalReplyApp/deploy
```

### 2. **配置环境变量**
```bash
cp .env.example .env
nano .env  # 编辑配置文件
```

### 3. **执行部署**
```bash
# Docker部署（推荐）
./deploy-all.sh prod docker

# 传统部署
./deploy-all.sh prod traditional
```

### 4. **验证部署**
- [ ] 后端API：http://server-ip:8080/api/actuator/health
- [ ] 管理后台：http://server-ip:3000 (Grafana)
- [ ] APP连接测试：配置APP后端地址为 http://server-ip:8080

## 🔍 **部署后检查**

### ✅ **功能测试**
- [ ] 用户注册/登录功能
- [ ] 情感分析功能
- [ ] VIP激活功能
- [ ] 消息历史记录
- [ ] 主题切换功能

### ✅ **性能检查**
- [ ] 页面加载速度 < 3秒
- [ ] API响应时间 < 1秒
- [ ] 数据库连接正常
- [ ] Redis缓存正常

### ✅ **安全检查**
- [ ] HTTPS正常工作
- [ ] 安全头配置正确
- [ ] 敏感信息不暴露
- [ ] 权限控制正常

## 🚨 **常见问题解决**

### 数据库连接失败
```bash
# 检查数据库容器状态
docker-compose ps mysql

# 查看数据库日志
docker-compose logs mysql
```

### 前端页面无法访问
```bash
# 检查Nginx配置
docker-compose exec frontend nginx -t

# 重启Nginx
docker-compose restart frontend
```

### 后端API异常
```bash
# 查看后端日志
docker-compose logs backend

# 检查健康状态
curl http://localhost:8080/api/actuator/health
```

## 📞 **技术支持**

如遇到部署问题，请检查：
1. 日志文件：`./logs/`
2. 配置文件：`.env`
3. 容器状态：`docker-compose ps`

## 🔄 **更新部署**

```bash
# 拉取最新代码
git pull origin main

# 重新部署
./deploy-all.sh prod docker
```

## 📊 **监控地址**

- **应用监控**: http://your-domain.com:3000
- **指标收集**: http://your-domain.com:9090
- **健康检查**: http://your-domain.com/api/actuator/health
