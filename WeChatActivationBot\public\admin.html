<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信激活码机器人 - 管理后台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .card h2 {
            color: #333;
            margin-bottom: 20px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        button {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        button:hover {
            background: #5a6fd8;
        }
        
        .btn-danger {
            background: #e74c3c;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .table th, .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        .table tr:hover {
            background-color: #f5f5f5;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        
        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .alert-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        .checkbox-cell {
            width: 50px;
            text-align: center;
        }

        .code-checkbox:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .table tr:has(.code-checkbox:disabled) {
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 微信自动回复机器人</h1>
            <p>管理后台</p>
            <div style="float: right; margin-top: -40px;">
                <a href="/users.html" style="margin-right: 15px; color: #3498db; text-decoration: none;">👥 用户管理</a>
                <span id="userInfo" style="margin-right: 15px; color: #666;"></span>
                <button onclick="logout()" class="btn-secondary">退出登录</button>
            </div>
        </div>

        <div id="alerts"></div>

        <!-- 统计信息 -->
        <div class="stats" id="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalKeywords">-</div>
                <div class="stat-label">关键字总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalCodes">-</div>
                <div class="stat-label">激活码总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="usedCodes">-</div>
                <div class="stat-label">已使用</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="availableCodes">-</div>
                <div class="stat-label">可用</div>
            </div>
        </div>

        <!-- 添加关键字 -->
        <div class="card">
            <h2>➕ 添加关键字</h2>
            <form id="addKeywordForm">
                <div class="form-group">
                    <label for="keyword">关键字</label>
                    <input type="text" id="keyword" name="keyword" required>
                </div>
                <div class="form-group">
                    <label for="description">描述</label>
                    <textarea id="description" name="description" rows="3"></textarea>
                </div>
                <button type="submit">添加关键字</button>
            </form>
        </div>

        <!-- 关键字列表 -->
        <div class="card">
            <h2>📝 关键字管理</h2>
            <div class="loading" id="keywordsLoading">加载中...</div>
            <div id="keywordsContent" style="display: none;">
                <table class="table" id="keywordsTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>关键字</th>
                            <th>描述</th>
                            <th>激活码统计</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="keywordsTableBody">
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 激活码管理 -->
        <div class="card" id="codesSection" style="display: none;">
            <h2>🎫 激活码管理</h2>
            <div id="codesContent">
                <div class="form-group">
                    <button onclick="toggleDeleteMode()" id="deleteToggleBtn" class="btn-danger">多选删除已使用</button>
                    <button onclick="deleteSelectedCodes()" id="deleteSelectedBtn" class="btn-danger" style="display:none;">删除选中项</button>
                    <button onclick="cancelDeleteMode()" id="cancelDeleteBtn" class="btn-secondary" style="display:none;">取消</button>
                </div>
                <div id="codesTable"></div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '/admin';
        let currentKeywordId = null;

        // 显示提示信息
        function showAlert(message, type = 'success') {
            const alertsDiv = document.getElementById('alerts');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            alertsDiv.appendChild(alert);
            
            setTimeout(() => {
                alert.remove();
            }, 5000);
        }

        // 加载统计信息
        async function loadStats() {
            try {
                const response = await fetch(`${API_BASE}/stats`);
                const data = await response.json();
                
                if (data.success) {
                    const stats = data.data;
                    document.getElementById('totalKeywords').textContent = stats.totalKeywords;
                    
                    let totalCodes = 0, usedCodes = 0, availableCodes = 0;
                    stats.keywordStats.forEach(stat => {
                        totalCodes += stat.total;
                        usedCodes += stat.used;
                        availableCodes += stat.available;
                    });
                    
                    document.getElementById('totalCodes').textContent = totalCodes;
                    document.getElementById('usedCodes').textContent = usedCodes;
                    document.getElementById('availableCodes').textContent = availableCodes;
                }
            } catch (error) {
                console.error('加载统计信息失败:', error);
            }
        }

        // 加载关键字列表
        async function loadKeywords() {
            try {
                const response = await fetch(`${API_BASE}/keywords`);
                const data = await response.json();
                
                if (data.success) {
                    const tbody = document.getElementById('keywordsTableBody');
                    tbody.innerHTML = '';
                    
                    for (const keyword of data.data) {
                        const statsResponse = await fetch(`${API_BASE}/keywords/${keyword.id}/stats`);
                        const statsData = await statsResponse.json();
                        const stats = statsData.success ? statsData.data : { total: 0, used: 0, available: 0 };
                        
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${keyword.id}</td>
                            <td><strong>${keyword.keyword}</strong></td>
                            <td>${keyword.description || '-'}</td>
                            <td>总计: ${stats.total} | 已用: ${stats.used} | 可用: ${stats.available}</td>
                            <td>
                                <button onclick="manageCodes(${keyword.id}, '${keyword.keyword}')" class="btn-success">管理激活码</button>
                                <button onclick="deleteKeyword(${keyword.id})" class="btn-danger">删除</button>
                            </td>
                        `;
                        tbody.appendChild(row);
                    }
                    
                    document.getElementById('keywordsLoading').style.display = 'none';
                    document.getElementById('keywordsContent').style.display = 'block';
                }
            } catch (error) {
                console.error('加载关键字失败:', error);
                showAlert('加载关键字失败', 'error');
            }
        }

        // 添加关键字
        document.getElementById('addKeywordForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const data = {
                keyword: formData.get('keyword'),
                description: formData.get('description')
            };
            
            try {
                const response = await fetch(`${API_BASE}/keywords`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert('关键字添加成功');
                    e.target.reset();
                    loadKeywords();
                    loadStats();
                } else {
                    showAlert('添加失败: ' + result.message, 'error');
                }
            } catch (error) {
                showAlert('添加失败: ' + error.message, 'error');
            }
        });

        // 删除关键字
        async function deleteKeyword(id) {
            if (!confirm('确定要删除这个关键字吗？这将同时删除所有相关的激活码。')) {
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/keywords/${id}`, {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert('关键字删除成功');
                    loadKeywords();
                    loadStats();
                } else {
                    showAlert('删除失败: ' + result.message, 'error');
                }
            } catch (error) {
                showAlert('删除失败: ' + error.message, 'error');
            }
        }

        // 管理激活码
        function manageCodes(keywordId, keywordName) {
            currentKeywordId = keywordId;
            document.getElementById('codesSection').style.display = 'block';
            document.querySelector('#codesSection h2').textContent = `🎫 "${keywordName}" 的激活码管理`;
            loadCodes(keywordId);
        }

        // 生成激活码
        async function generateCodes() {
            if (!currentKeywordId) return;
            
            const count = document.getElementById('generateCount').value;
            
            try {
                const response = await fetch(`${API_BASE}/keywords/${currentKeywordId}/codes/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ count: parseInt(count) })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert(`成功生成 ${count} 个激活码`);
                    loadCodes(currentKeywordId);
                    loadKeywords();
                    loadStats();
                } else {
                    showAlert('生成失败: ' + result.message, 'error');
                }
            } catch (error) {
                showAlert('生成失败: ' + error.message, 'error');
            }
        }

        // 加载激活码列表
        async function loadCodes(keywordId) {
            try {
                const response = await fetch(`${API_BASE}/keywords/${keywordId}/codes?limit=100`);
                const data = await response.json();
                
                if (data.success) {
                    const codesDiv = document.getElementById('codesTable');
                    
                    if (data.data.codes.length === 0) {
                        codesDiv.innerHTML = '<p>暂无激活码，请先生成。</p>';
                        return;
                    }
                    
                    let html = `
                        <h3>激活码列表 (显示前100个)</h3>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th id="selectAllHeader" style="display:none;">
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()"> 全选
                                    </th>
                                    <th>激活码</th>
                                    <th>状态</th>
                                    <th>使用者</th>
                                    <th>使用时间</th>
                                </tr>
                            </thead>
                            <tbody>
                    `;
                    
                    data.data.codes.forEach(code => {
                        const status = code.used_by ? '已使用' : '可用';
                        const usedBy = code.used_by || '-';
                        const usedAt = code.used_at ? new Date(code.used_at).toLocaleString() : '-';
                        const isUsed = code.used_by ? true : false;

                        html += `
                            <tr>
                                <td class="checkbox-cell" style="display:none;">
                                    <input type="checkbox" class="code-checkbox" value="${code.id}" ${!isUsed ? 'disabled' : ''}>
                                </td>
                                <td><code>${code.code}</code></td>
                                <td>${status}</td>
                                <td>${usedBy}</td>
                                <td>${usedAt}</td>
                            </tr>
                        `;
                    });
                    
                    html += '</tbody></table>';
                    codesDiv.innerHTML = html;
                }
            } catch (error) {
                console.error('加载激活码失败:', error);
            }
        }

        // 切换删除模式
        function toggleDeleteMode() {
            const checkboxCells = document.querySelectorAll('.checkbox-cell');
            const selectAllHeader = document.getElementById('selectAllHeader');
            const deleteToggleBtn = document.getElementById('deleteToggleBtn');
            const deleteSelectedBtn = document.getElementById('deleteSelectedBtn');
            const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');

            // 显示复选框
            checkboxCells.forEach(cell => cell.style.display = 'table-cell');
            selectAllHeader.style.display = 'table-cell';

            // 切换按钮显示
            deleteToggleBtn.style.display = 'none';
            deleteSelectedBtn.style.display = 'inline-block';
            cancelDeleteBtn.style.display = 'inline-block';
        }

        // 取消删除模式
        function cancelDeleteMode() {
            const checkboxCells = document.querySelectorAll('.checkbox-cell');
            const selectAllHeader = document.getElementById('selectAllHeader');
            const deleteToggleBtn = document.getElementById('deleteToggleBtn');
            const deleteSelectedBtn = document.getElementById('deleteSelectedBtn');
            const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
            const selectAll = document.getElementById('selectAll');

            // 隐藏复选框
            checkboxCells.forEach(cell => cell.style.display = 'none');
            selectAllHeader.style.display = 'none';

            // 清除所有选择
            selectAll.checked = false;
            document.querySelectorAll('.code-checkbox').forEach(cb => cb.checked = false);

            // 切换按钮显示
            deleteToggleBtn.style.display = 'inline-block';
            deleteSelectedBtn.style.display = 'none';
            cancelDeleteBtn.style.display = 'none';
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.code-checkbox:not([disabled])');

            checkboxes.forEach(cb => {
                cb.checked = selectAll.checked;
            });
        }

        // 删除选中的激活码
        async function deleteSelectedCodes() {
            const selectedCheckboxes = document.querySelectorAll('.code-checkbox:checked');

            if (selectedCheckboxes.length === 0) {
                showAlert('请选择要删除的激活码', 'warning');
                return;
            }

            if (!confirm(`确定要删除选中的 ${selectedCheckboxes.length} 个已使用的激活码吗？`)) {
                return;
            }

            try {
                const codeIds = Array.from(selectedCheckboxes).map(cb => cb.value);

                const response = await fetch(`${API_BASE}/keywords/${currentKeywordId}/codes/batch-delete`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ codeIds })
                });

                const result = await response.json();

                if (result.success) {
                    showAlert(`成功删除 ${result.data.deletedCount} 个激活码`, 'success');
                    cancelDeleteMode();
                    loadCodes(currentKeywordId);
                    loadKeywords(); // 刷新统计
                } else {
                    showAlert('删除失败: ' + result.message, 'error');
                }
            } catch (error) {
                showAlert('删除失败: ' + error.message, 'error');
            }
        }

        // 检查登录状态
        async function checkAuth() {
            try {
                const response = await fetch(`${API_BASE}/auth/status`);
                const data = await response.json();

                if (!data.success || !data.data.authenticated) {
                    // 未登录，跳转到登录页面
                    window.location.href = '/login.html';
                    return false;
                }

                // 显示用户信息
                document.getElementById('userInfo').textContent = `欢迎，${data.data.username}`;
                return true;
            } catch (error) {
                console.error('检查登录状态失败:', error);
                window.location.href = '/login.html';
                return false;
            }
        }

        // 退出登录
        async function logout() {
            if (!confirm('确定要退出登录吗？')) {
                return;
            }

            try {
                await fetch(`${API_BASE}/auth/logout`, {
                    method: 'POST'
                });
            } catch (error) {
                console.error('退出登录失败:', error);
            }

            // 跳转到登录页面
            window.location.href = '/login.html';
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', async () => {
            // 先检查登录状态
            const isAuthenticated = await checkAuth();
            if (isAuthenticated) {
                loadStats();
                loadKeywords();
            }
        });
    </script>
</body>
</html>
