const Logger = require('../utils/logger');

class KeywordMatcher {
    constructor(databaseService) {
        this.db = databaseService;
        this.keywordCache = new Map();
        this.cacheExpiry = 5 * 60 * 1000; // 5分钟缓存
        this.lastCacheUpdate = 0;
    }

    /**
     * 查找匹配的关键字
     */
    async findMatch(inputKeyword) {
        try {
            await this.updateCacheIfNeeded();
            
            const normalizedInput = this.normalizeKeyword(inputKeyword);
            
            // 精确匹配
            let match = this.findExactMatch(normalizedInput);
            if (match) {
                Logger.info(`精确匹配关键字: ${inputKeyword} -> ${match.keyword}`);
                return match;
            }

            // 模糊匹配
            match = this.findFuzzyMatch(normalizedInput);
            if (match) {
                Logger.info(`模糊匹配关键字: ${inputKeyword} -> ${match.keyword}`);
                return match;
            }

            Logger.info(`未找到匹配的关键字: ${inputKeyword}`);
            return null;

        } catch (error) {
            Logger.error('关键字匹配失败:', error);
            throw error;
        }
    }

    /**
     * 更新缓存（如果需要）
     */
    async updateCacheIfNeeded() {
        const now = Date.now();
        if (now - this.lastCacheUpdate > this.cacheExpiry) {
            await this.updateCache();
            this.lastCacheUpdate = now;
        }
    }

    /**
     * 更新关键字缓存
     */
    async updateCache() {
        try {
            const keywords = await this.db.getAllKeywords();
            this.keywordCache.clear();
            
            keywords.forEach(keyword => {
                const normalized = this.normalizeKeyword(keyword.keyword);
                this.keywordCache.set(normalized, keyword);
                
                // 同时存储原始关键字
                if (normalized !== keyword.keyword) {
                    this.keywordCache.set(keyword.keyword, keyword);
                }
            });
            
            Logger.info(`关键字缓存已更新，共 ${keywords.length} 个关键字`);
        } catch (error) {
            Logger.error('更新关键字缓存失败:', error);
            throw error;
        }
    }

    /**
     * 标准化关键字（去除空格、转小写等）
     */
    normalizeKeyword(keyword) {
        return keyword.trim().toLowerCase().replace(/\s+/g, '');
    }

    /**
     * 精确匹配
     */
    findExactMatch(normalizedKeyword) {
        return this.keywordCache.get(normalizedKeyword) || null;
    }

    /**
     * 模糊匹配
     */
    findFuzzyMatch(normalizedKeyword) {
        // 遍历所有缓存的关键字进行模糊匹配
        for (const [cachedKeyword, keywordData] of this.keywordCache) {
            if (this.isFuzzyMatch(normalizedKeyword, cachedKeyword)) {
                return keywordData;
            }
        }
        return null;
    }

    /**
     * 判断是否为模糊匹配
     */
    isFuzzyMatch(input, target) {
        // 包含匹配
        if (target.includes(input) || input.includes(target)) {
            return true;
        }

        // 编辑距离匹配（适用于短关键字）
        if (input.length <= 10 && target.length <= 10) {
            const distance = this.calculateEditDistance(input, target);
            const maxLength = Math.max(input.length, target.length);
            const similarity = 1 - (distance / maxLength);
            return similarity >= 0.7; // 70%相似度
        }

        return false;
    }

    /**
     * 计算编辑距离
     */
    calculateEditDistance(str1, str2) {
        const matrix = [];
        
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }
        
        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }
        
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }
        
        return matrix[str2.length][str1.length];
    }

    /**
     * 添加新关键字
     */
    async addKeyword(keyword, description = '') {
        try {
            const keywordData = await this.db.addKeyword(keyword, description);
            
            // 更新缓存
            const normalized = this.normalizeKeyword(keyword);
            this.keywordCache.set(normalized, keywordData);
            this.keywordCache.set(keyword, keywordData);
            
            Logger.info(`添加新关键字: ${keyword}`);
            return keywordData;
        } catch (error) {
            Logger.error('添加关键字失败:', error);
            throw error;
        }
    }

    /**
     * 删除关键字
     */
    async removeKeyword(keywordId) {
        try {
            await this.db.removeKeyword(keywordId);
            
            // 从缓存中移除
            for (const [key, value] of this.keywordCache) {
                if (value.id === keywordId) {
                    this.keywordCache.delete(key);
                }
            }
            
            Logger.info(`删除关键字: ${keywordId}`);
        } catch (error) {
            Logger.error('删除关键字失败:', error);
            throw error;
        }
    }

    /**
     * 获取所有关键字
     */
    async getAllKeywords() {
        return await this.db.getAllKeywords();
    }
}

module.exports = KeywordMatcher;
