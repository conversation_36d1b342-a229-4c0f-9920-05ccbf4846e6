/**
 * 路由守卫工具
 * 用于控制页面访问权限
 */

import { UserManager } from './user.js'

/**
 * 路由守卫类
 */
export class AuthGuard {

  // 防止重复导航的标志
  static isNavigating = false
  
  /**
   * 需要登录的页面列表
   */
  static PROTECTED_PAGES = [
    'pages/index/index',
    'pages/message/input',
    'pages/reply/generation',
    'pages/history/history',
    'pages/settings/settings',
    'pages/user/profile',
    'pages/user/change-password'
  ]
  
  /**
   * 登录相关页面列表
   */
  static AUTH_PAGES = [
    'pages/login/login',
    'pages/user/register',
    'pages/user/forgot-password'
  ]
  
  /**
   * 检查页面访问权限
   * @param {string} route - 页面路径
   * @returns {boolean} 是否有权限访问
   */
  static checkPageAccess(route) {
    const isLoggedIn = UserManager.isLoggedIn()
    const isProtectedPage = this.PROTECTED_PAGES.includes(route)
    const isAuthPage = this.AUTH_PAGES.includes(route)
    
    // 如果是受保护的页面但未登录，拒绝访问
    if (isProtectedPage && !isLoggedIn) {
      return false
    }
    
    // 如果是登录页面但已登录，拒绝访问
    if (isAuthPage && isLoggedIn) {
      return false
    }
    
    return true
  }
  
  /**
   * 重定向到合适的页面
   * @param {string} currentRoute - 当前页面路径
   */
  static redirectToAppropiatePage(currentRoute) {
    // 防止重复导航
    if (this.isNavigating) {
      return
    }

    const isLoggedIn = UserManager.isLoggedIn()

    this.isNavigating = true

    if (!isLoggedIn) {
      // 未登录，跳转到登录页
      uni.reLaunch({
        url: '/pages/login/login',
        success: () => {
          setTimeout(() => {
            this.isNavigating = false
          }, 1000)
        },
        fail: () => {
          this.isNavigating = false
        }
      })
    } else {
      // 已登录，跳转到首页
      uni.reLaunch({
        url: '/pages/index/index',
        success: () => {
          setTimeout(() => {
            this.isNavigating = false
          }, 1000)
        },
        fail: () => {
          this.isNavigating = false
        }
      })
    }
  }
  
  /**
   * 页面守卫中间件
   * 在页面 onLoad 中调用
   * @param {string} route - 页面路径
   * @returns {boolean} 是否允许继续加载页面
   */
  static pageGuard(route) {
    const hasAccess = this.checkPageAccess(route)
    
    if (!hasAccess) {
      // 延迟执行重定向，避免页面加载冲突
      setTimeout(() => {
        this.redirectToAppropiatePage(route)
      }, 100)
      return false
    }
    
    return true
  }
  
  /**
   * 检查是否为受保护页面
   * @param {string} route - 页面路径
   * @returns {boolean} 是否为受保护页面
   */
  static isProtectedPage(route) {
    return this.PROTECTED_PAGES.includes(route)
  }
  
  /**
   * 检查是否为登录相关页面
   * @param {string} route - 页面路径
   * @returns {boolean} 是否为登录相关页面
   */
  static isAuthPage(route) {
    return this.AUTH_PAGES.includes(route)
  }
  
  /**
   * 获取当前页面路径
   * @returns {string} 当前页面路径
   */
  static getCurrentRoute() {
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    return currentPage ? currentPage.route : ''
  }
  
  /**
   * 登录成功后的重定向
   * @param {string} redirectUrl - 重定向URL，可选
   */
  static loginSuccessRedirect(redirectUrl = null) {
    const targetUrl = redirectUrl || '/pages/index/index'
    
    uni.reLaunch({
      url: targetUrl
    })
  }
  
  /**
   * 退出登录后的重定向
   */
  static logoutRedirect() {
    uni.reLaunch({
      url: '/pages/login/login'
    })
  }
}

export default AuthGuard
