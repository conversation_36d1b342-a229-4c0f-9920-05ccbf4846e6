package com.emotional.service.controller;

import com.emotional.service.common.Result;
import com.emotional.service.service.VipMonitorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * VIP监控管理控制器
 * 用于管理和查看VIP监控服务状态
 */
@Slf4j
@RestController
@RequestMapping("/admin/vip-monitor")
@RequiredArgsConstructor
@CrossOrigin(originPatterns = "*", maxAge = 3600)
@Tag(name = "VIP监控管理", description = "VIP监控服务状态查看和管理")
public class VipMonitorController {

    private final VipMonitorService vipMonitorService;

    /**
     * 获取VIP监控服务状态
     */
    @Operation(summary = "获取监控状态", description = "查看当前VIP监控服务的状态信息")
    @GetMapping("/status")
    public Result<Map<String, Object>> getMonitorStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            status.put("monitoredUserCount", vipMonitorService.getMonitoredUserCount());
            status.put("status", "running");
            status.put("description", "VIP监控服务正在运行");
            
            // 获取系统内存信息
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            
            Map<String, Object> memoryInfo = new HashMap<>();
            memoryInfo.put("totalMemoryMB", totalMemory / 1024 / 1024);
            memoryInfo.put("usedMemoryMB", usedMemory / 1024 / 1024);
            memoryInfo.put("freeMemoryMB", freeMemory / 1024 / 1024);
            
            status.put("memoryInfo", memoryInfo);
            
            return Result.success("获取监控状态成功", status);
            
        } catch (Exception e) {
            log.error("获取VIP监控状态失败", e);
            return Result.error("获取监控状态失败: " + e.getMessage());
        }
    }

    /**
     * 清空监控容器（仅用于测试和维护）
     */
    @Operation(summary = "清空监控容器", description = "清空VIP监控容器中的所有数据（仅用于测试）")
    @PostMapping("/clear")
    public Result<String> clearMonitor() {
        try {
            vipMonitorService.clearMonitor();
            log.info("VIP监控容器已被手动清空");
            return Result.success("监控容器已清空");
            
        } catch (Exception e) {
            log.error("清空VIP监控容器失败", e);
            return Result.error("清空监控容器失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发VIP过期检查
     */
    @Operation(summary = "手动检查过期用户", description = "手动触发VIP过期用户检查任务")
    @PostMapping("/check-expired")
    public Result<String> checkExpiredUsers() {
        try {
            vipMonitorService.checkExpiringUsers();
            log.info("手动触发VIP过期检查完成");
            return Result.success("VIP过期检查已完成");
            
        } catch (Exception e) {
            log.error("手动VIP过期检查失败", e);
            return Result.error("VIP过期检查失败: " + e.getMessage());
        }
    }
}
