<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 微信自动回复机器人</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #2c3e50;
            margin: 0;
        }

        .nav-links {
            display: flex;
            gap: 15px;
        }

        .nav-links a {
            color: #3498db;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .nav-links a:hover {
            background-color: #ecf0f1;
        }

        .nav-links a.active {
            background-color: #3498db;
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .controls {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .search-box {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background-color: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2980b9;
        }

        .btn-secondary {
            background-color: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #7f8c8d;
        }

        .users-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        tr:hover {
            background-color: #f8f9fa;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-subscribed {
            background-color: #d4edda;
            color: #155724;
        }

        .status-unsubscribed {
            background-color: #f8d7da;
            color: #721c24;
        }

        .code-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-family: monospace;
            background-color: #e9ecef;
            color: #495057;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .pagination button:hover {
            background-color: #f8f9fa;
        }

        .pagination button.active {
            background-color: #3498db;
            color: white;
            border-color: #3498db;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .user-detail {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-top: 20px;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .detail-item {
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 6px;
        }

        .detail-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .detail-value {
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>👥 用户管理</h1>
            <div class="nav-links">
                <a href="/admin.html">关键词管理</a>
                <a href="/users.html" class="active">用户管理</a>
                <a href="#" onclick="logout()">退出登录</a>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="stats-grid" id="statsGrid">
            <div class="stat-card">
                <div class="stat-number" id="totalUsers">-</div>
                <div class="stat-label">总用户数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="subscribedUsers">-</div>
                <div class="stat-label">关注用户</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="usersWithCodes">-</div>
                <div class="stat-label">已获取激活码</div>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="controls">
            <input type="text" class="search-box" id="searchInput" placeholder="搜索用户 (OpenID 或昵称)">
            <button class="btn btn-primary" onclick="searchUsers()">搜索</button>
            <button class="btn btn-secondary" onclick="clearSearch()">清空</button>
            <button class="btn btn-primary" onclick="refreshUsers()">刷新</button>
        </div>

        <!-- 用户列表 -->
        <div class="users-table">
            <table>
                <thead>
                    <tr>
                        <th>OpenID</th>
                        <th>昵称</th>
                        <th>关注状态</th>
                        <th>关注时间</th>
                        <th>激活码</th>
                        <th>获取时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="usersTableBody">
                    <tr>
                        <td colspan="7" class="loading">加载中...</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        <div class="pagination" id="pagination">
            <!-- 分页按钮将通过JavaScript生成 -->
        </div>

        <!-- 用户详情模态框 -->
        <div id="userDetail" class="user-detail" style="display: none;">
            <h3>用户详情</h3>
            <div class="detail-grid" id="userDetailContent">
                <!-- 详情内容将通过JavaScript生成 -->
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '/admin';
        let currentPage = 1;
        let currentSearch = '';
        const pageSize = 20;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', async () => {
            await checkAuth();
            await loadStats();
            await loadUsers();
        });

        // 检查登录状态
        async function checkAuth() {
            try {
                const response = await fetch(`${API_BASE}/auth/status`, {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    console.error('认证检查失败:', response.status);
                    window.location.href = '/login.html';
                    return false;
                }

                const data = await response.json();

                if (!data.success || !data.data.authenticated) {
                    console.log('用户未认证，跳转到登录页面');
                    window.location.href = '/login.html';
                    return false;
                }

                console.log('用户认证成功:', data.data.username);
                return true;
            } catch (error) {
                console.error('检查登录状态失败:', error);
                window.location.href = '/login.html';
                return false;
            }
        }

        // 退出登录
        async function logout() {
            if (!confirm('确定要退出登录吗？')) return;
            
            try {
                await fetch(`${API_BASE}/auth/logout`, { method: 'POST' });
            } catch (error) {
                console.error('退出登录失败:', error);
            }
            
            window.location.href = '/login.html';
        }

        // 加载统计信息
        async function loadStats() {
            try {
                const response = await fetch(`${API_BASE}/stats`, {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (response.status === 401) {
                    window.location.href = '/login.html';
                    return;
                }

                const data = await response.json();

                if (data.success && data.data.users) {
                    const users = data.data.users;
                    document.getElementById('totalUsers').textContent = users.total_users || 0;
                    document.getElementById('subscribedUsers').textContent = users.subscribed_users || 0;
                    document.getElementById('usersWithCodes').textContent = users.users_with_codes || 0;
                }
            } catch (error) {
                console.error('加载统计信息失败:', error);
            }
        }

        // 加载用户列表
        async function loadUsers(page = 1, search = '') {
            try {
                currentPage = page;
                currentSearch = search;

                const params = new URLSearchParams({
                    page: page,
                    limit: pageSize,
                    search: search
                });

                console.log('正在加载用户列表:', `${API_BASE}/users?${params}`);

                const response = await fetch(`${API_BASE}/users?${params}`, {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                console.log('API响应状态:', response.status);

                if (response.status === 401) {
                    console.log('未认证，跳转到登录页面');
                    window.location.href = '/login.html';
                    return;
                }

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    const text = await response.text();
                    console.error('API返回非JSON数据:', text.substring(0, 200));
                    throw new Error('服务器返回了非JSON数据，可能是认证问题');
                }

                const data = await response.json();
                console.log('用户数据:', data);

                if (data.success) {
                    renderUsers(data.data.users);
                    renderPagination(data.data.pagination);
                } else {
                    showError(data.message || '加载用户列表失败');
                }
            } catch (error) {
                console.error('加载用户列表失败:', error);
                showError('加载用户列表失败: ' + error.message);
            }
        }

        // 渲染用户列表
        function renderUsers(users) {
            const tbody = document.getElementById('usersTableBody');
            
            if (users.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="empty-state">暂无用户数据</td></tr>';
                return;
            }
            
            tbody.innerHTML = users.map(user => `
                <tr>
                    <td title="${user.openid}">${user.openid.substring(0, 20)}...</td>
                    <td>${user.nickname || '未设置'}</td>
                    <td>
                        <span class="status-badge ${user.is_subscribed ? 'status-subscribed' : 'status-unsubscribed'}">
                            ${user.is_subscribed ? '已关注' : '未关注'}
                        </span>
                    </td>
                    <td>${formatDateTime(user.subscribe_time)}</td>
                    <td>
                        ${user.activation_code_used ? 
                            `<span class="code-badge">${user.activation_code_used}</span>` : 
                            '<span style="color: #999;">未获取</span>'
                        }
                    </td>
                    <td>${formatDateTime(user.activation_code_time)}</td>
                    <td>
                        <button class="btn btn-primary" onclick="viewUserDetail('${user.openid}')">详情</button>
                    </td>
                </tr>
            `).join('');
        }

        // 渲染分页
        function renderPagination(pagination) {
            const paginationDiv = document.getElementById('pagination');
            const { page, pages, total } = pagination;
            
            let html = `
                <button onclick="loadUsers(1, currentSearch)" ${page === 1 ? 'disabled' : ''}>首页</button>
                <button onclick="loadUsers(${page - 1}, currentSearch)" ${page === 1 ? 'disabled' : ''}>上一页</button>
                <span>第 ${page} 页，共 ${pages} 页 (${total} 条记录)</span>
                <button onclick="loadUsers(${page + 1}, currentSearch)" ${page === pages ? 'disabled' : ''}>下一页</button>
                <button onclick="loadUsers(${pages}, currentSearch)" ${page === pages ? 'disabled' : ''}>末页</button>
            `;
            
            paginationDiv.innerHTML = html;
        }

        // 搜索用户
        function searchUsers() {
            const search = document.getElementById('searchInput').value.trim();
            loadUsers(1, search);
        }

        // 清空搜索
        function clearSearch() {
            document.getElementById('searchInput').value = '';
            loadUsers(1, '');
        }

        // 刷新用户列表
        function refreshUsers() {
            loadUsers(currentPage, currentSearch);
            loadStats();
        }

        // 查看用户详情
        async function viewUserDetail(openid) {
            try {
                const response = await fetch(`${API_BASE}/users/${openid}`, {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (response.status === 401) {
                    window.location.href = '/login.html';
                    return;
                }

                const data = await response.json();

                if (data.success) {
                    renderUserDetail(data.data);
                } else {
                    showError(data.message || '获取用户详情失败');
                }
            } catch (error) {
                console.error('获取用户详情失败:', error);
                showError('获取用户详情失败: ' + error.message);
            }
        }

        // 渲染用户详情
        function renderUserDetail(user) {
            const detailDiv = document.getElementById('userDetail');
            const contentDiv = document.getElementById('userDetailContent');
            
            contentDiv.innerHTML = `
                <div class="detail-item">
                    <div class="detail-label">OpenID</div>
                    <div class="detail-value">${user.openid}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">昵称</div>
                    <div class="detail-value">${user.nickname || '未设置'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">关注状态</div>
                    <div class="detail-value">
                        <span class="status-badge ${user.is_subscribed ? 'status-subscribed' : 'status-unsubscribed'}">
                            ${user.is_subscribed ? '已关注' : '未关注'}
                        </span>
                    </div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">关注时间</div>
                    <div class="detail-value">${formatDateTime(user.subscribe_time)}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">取消关注时间</div>
                    <div class="detail-value">${formatDateTime(user.unsubscribe_time)}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">激活码</div>
                    <div class="detail-value">
                        ${user.activation_code_used ? 
                            `<span class="code-badge">${user.activation_code_used}</span>` : 
                            '未获取'
                        }
                    </div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">激活码获取时间</div>
                    <div class="detail-value">${formatDateTime(user.activation_code_time)}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">创建时间</div>
                    <div class="detail-value">${formatDateTime(user.created_at)}</div>
                </div>
            `;
            
            detailDiv.style.display = 'block';
            detailDiv.scrollIntoView({ behavior: 'smooth' });
        }

        // 格式化日期时间
        function formatDateTime(dateString) {
            if (!dateString) return '-';
            
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }

        // 显示错误信息
        function showError(message) {
            alert(message);
        }

        // 回车搜索
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchUsers();
            }
        });
    </script>
</body>
</html>
