[2025-07-10T04:49:39.571Z] INFO  微信服务初始化成功
[2025-07-10T04:49:39.576Z] INFO  中间件设置完成
[2025-07-10T04:49:39.577Z] INFO  路由设置完成
[2025-07-10T04:49:39.577Z] INFO  错误处理设置完成
[2025-07-10T04:49:39.603Z] INFO  🚀 WeChat Official Bot 启动成功 | {"type":"startup","service":"WeChat Official Bot","port":"3001","host":"0.0.0.0","env":"development","pid":492}
[2025-07-10T04:50:55.797Z] INFO  微信服务初始化成功
[2025-07-10T04:50:55.804Z] INFO  中间件设置完成
[2025-07-10T04:50:55.805Z] INFO  路由设置完成
[2025-07-10T04:50:55.806Z] INFO  错误处理设置完成
[2025-07-10T04:50:55.829Z] ERROR 未捕获的异常 | {"error":"listen EADDRINUSE: address already in use 0.0.0.0:3001","stack":"Error: listen EADDRINUSE: address already in use 0.0.0.0:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1817:16)\n    at listenInCluster (node:net:1865:12)\n    at doListen (node:net:2014:7)\n    at process.processTicksAndRejections (node:internal/process/task_queues:83:21)"}
[2025-07-10T04:50:55.833Z] INFO  🛑 WeChat Official Bot 关闭 | {"type":"shutdown","service":"WeChat Official Bot","reason":"uncaughtException"}
[2025-07-10T04:50:55.837Z] INFO  服务器已关闭
[2025-07-10T04:53:32.816Z] INFO  微信服务初始化成功
[2025-07-10T04:53:32.823Z] INFO  中间件设置完成
[2025-07-10T04:53:32.824Z] INFO  路由设置完成
[2025-07-10T04:53:32.825Z] INFO  错误处理设置完成
[2025-07-10T04:53:32.846Z] INFO  🚀 WeChat Official Bot 启动成功 | {"type":"startup","service":"WeChat Official Bot","port":"3001","host":"0.0.0.0","env":"development","pid":14632}
[2025-07-10T04:54:01.297Z] INFO  127.0.0.1 - - [10/Jul/2025:04:54:01 +0000] "GET /health HTTP/1.1" 200 250 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.19041.5965"
[2025-07-10T04:54:10.346Z] INFO  [微信] 收到微信验证请求 | {"type":"wechat","subType":"verify","signature":"undefined...","echostr":"undefined...","ip":"127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
[2025-07-10T04:54:10.347Z] WARN  微信验证参数不完整 | {}
[2025-07-10T04:54:10.349Z] INFO  127.0.0.1 - - [10/Jul/2025:04:54:10 +0000] "GET /wechat HTTP/1.1" 400 15 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-10T04:54:31.080Z] INFO  [微信] 签名验证失败 | {"type":"wechat","subType":"signature","error":"缺少必要参数"}
[2025-07-10T04:54:31.081Z] INFO  127.0.0.1 - - [10/Jul/2025:04:54:31 +0000] "GET /wechat/test?action=signature&timestamp=**********&nonce=test_nonce HTTP/1.1" 200 81 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.19041.5965"
[2025-07-10T04:55:58.107Z] INFO  [微信] 收到微信验证请求 | {"type":"wechat","subType":"verify","signature":"3ce1450e68...","timestamp":"1752123329","nonce":"HjDmFeBOh64kRRX3","echostr":"test_echo...","ip":"127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.19041.5965"}
[2025-07-10T04:55:58.108Z] INFO  [微信] 签名验证成功 | {"type":"wechat","subType":"signature","timeDiff":29}
[2025-07-10T04:55:58.108Z] INFO  [微信] ✅ 微信验证成功 | {"type":"wechat","subType":"verify"}
[2025-07-10T04:55:58.109Z] INFO  127.0.0.1 - - [10/Jul/2025:04:55:58 +0000] "GET /wechat?signature=3ce1450e681f973013edeb2ab92ce4f54a12f88a&timestamp=1752123329&nonce=HjDmFeBOh64kRRX3&echostr=test_echo HTTP/1.1" 200 9 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.19041.5965"
