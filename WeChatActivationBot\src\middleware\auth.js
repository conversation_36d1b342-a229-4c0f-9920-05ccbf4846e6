const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const Logger = require('../utils/logger');

class AuthMiddleware {
    constructor() {
        this.jwtSecret = process.env.JWT_SECRET || 'default_secret_change_in_production';
        this.adminUsername = process.env.ADMIN_USERNAME || 'admin';
        this.sessionTimeout = process.env.SESSION_TIMEOUT || '24h';

        // 支持两种密码配置方式：
        // 1. ADMIN_PASSWORD_HASH - 预先哈希的密码（推荐）
        // 2. ADMIN_PASSWORD - 明文密码（仅用于开发环境）
        this.adminPasswordHash = process.env.ADMIN_PASSWORD_HASH;

        if (!this.adminPasswordHash && process.env.ADMIN_PASSWORD) {
            // 如果没有哈希密码但有明文密码，则动态生成哈希
            Logger.warn('使用明文密码，建议使用ADMIN_PASSWORD_HASH');
            this.adminPasswordHash = bcrypt.hashSync(process.env.ADMIN_PASSWORD, 10);
        }

        if (!this.adminPasswordHash) {
            // 如果都没有，使用默认密码并警告
            Logger.error('未设置管理员密码，使用默认密码！请立即修改！');
            this.adminPasswordHash = bcrypt.hashSync('admin123456', 10);
        }

        Logger.info('认证中间件初始化完成');
    }

    /**
     * 验证用户凭据
     */
    async validateCredentials(username, password) {
        try {
            if (username !== this.adminUsername) {
                Logger.warn(`登录失败: 用户名错误 - ${username}`);
                return false;
            }

            const isValid = await bcrypt.compare(password, this.adminPasswordHash);
            if (!isValid) {
                Logger.warn(`登录失败: 密码错误 - ${username}`);
            }

            return isValid;
        } catch (error) {
            Logger.error('验证凭据失败:', error);
            return false;
        }
    }

    /**
     * 生成JWT令牌
     */
    generateToken(username) {
        try {
            const payload = {
                username,
                role: 'admin',
                iat: Math.floor(Date.now() / 1000)
            };
            
            return jwt.sign(payload, this.jwtSecret, { 
                expiresIn: this.sessionTimeout 
            });
        } catch (error) {
            Logger.error('生成令牌失败:', error);
            throw error;
        }
    }

    /**
     * 验证JWT令牌
     */
    verifyToken(token) {
        try {
            return jwt.verify(token, this.jwtSecret);
        } catch (error) {
            Logger.warn('令牌验证失败:', error.message);
            return null;
        }
    }

    /**
     * 认证中间件 - 保护管理员路由
     */
    requireAuth() {
        return (req, res, next) => {
            try {
                // 从多个地方获取令牌
                let token = null;
                
                // 1. 从Authorization头获取
                const authHeader = req.headers.authorization;
                if (authHeader && authHeader.startsWith('Bearer ')) {
                    token = authHeader.substring(7);
                }
                
                // 2. 从Cookie获取
                if (!token && req.cookies && req.cookies.adminToken) {
                    token = req.cookies.adminToken;
                }
                
                // 3. 从查询参数获取（用于某些特殊情况）
                if (!token && req.query.token) {
                    token = req.query.token;
                }
                
                if (!token) {
                    return res.status(401).json({ 
                        success: false, 
                        message: '未提供认证令牌',
                        code: 'NO_TOKEN'
                    });
                }
                
                const decoded = this.verifyToken(token);
                if (!decoded) {
                    return res.status(401).json({ 
                        success: false, 
                        message: '无效的认证令牌',
                        code: 'INVALID_TOKEN'
                    });
                }
                
                // 将用户信息添加到请求对象
                req.user = decoded;
                next();
                
            } catch (error) {
                Logger.error('认证中间件错误:', error);
                res.status(500).json({ 
                    success: false, 
                    message: '认证服务错误' 
                });
            }
        };
    }

    /**
     * 可选认证中间件 - 不强制要求认证，但会解析令牌
     */
    optionalAuth() {
        return (req, res, next) => {
            try {
                let token = null;
                
                const authHeader = req.headers.authorization;
                if (authHeader && authHeader.startsWith('Bearer ')) {
                    token = authHeader.substring(7);
                }
                
                if (!token && req.cookies && req.cookies.adminToken) {
                    token = req.cookies.adminToken;
                }
                
                if (token) {
                    const decoded = this.verifyToken(token);
                    if (decoded) {
                        req.user = decoded;
                    }
                }
                
                next();
            } catch (error) {
                Logger.error('可选认证中间件错误:', error);
                next(); // 继续执行，不阻止请求
            }
        };
    }

    /**
     * 登录路由处理器
     */
    async handleLogin(req, res) {
        try {
            const { username, password } = req.body;
            
            if (!username || !password) {
                return res.status(400).json({
                    success: false,
                    message: '用户名和密码不能为空'
                });
            }
            
            const isValid = await this.validateCredentials(username, password);
            if (!isValid) {
                Logger.warn(`登录失败: ${username} from ${req.ip}`);
                return res.status(401).json({
                    success: false,
                    message: '用户名或密码错误'
                });
            }
            
            const token = this.generateToken(username);
            
            // 设置Cookie（HttpOnly，安全）
            res.cookie('adminToken', token, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'strict',
                maxAge: 24 * 60 * 60 * 1000 // 24小时
            });
            
            Logger.info(`管理员登录成功: ${username} from ${req.ip}`);
            
            res.json({
                success: true,
                message: '登录成功',
                data: {
                    token,
                    username,
                    expiresIn: this.sessionTimeout
                }
            });
            
        } catch (error) {
            Logger.error('登录处理失败:', error);
            res.status(500).json({
                success: false,
                message: '登录服务错误'
            });
        }
    }

    /**
     * 登出路由处理器
     */
    handleLogout(req, res) {
        try {
            // 清除Cookie
            res.clearCookie('adminToken');
            
            Logger.info(`管理员登出: ${req.user?.username || 'unknown'} from ${req.ip}`);
            
            res.json({
                success: true,
                message: '登出成功'
            });
        } catch (error) {
            Logger.error('登出处理失败:', error);
            res.status(500).json({
                success: false,
                message: '登出服务错误'
            });
        }
    }

    /**
     * 检查认证状态
     */
    checkAuthStatus(req, res) {
        try {
            if (req.user) {
                res.json({
                    success: true,
                    data: {
                        authenticated: true,
                        username: req.user.username,
                        role: req.user.role
                    }
                });
            } else {
                res.json({
                    success: true,
                    data: {
                        authenticated: false
                    }
                });
            }
        } catch (error) {
            Logger.error('检查认证状态失败:', error);
            res.status(500).json({
                success: false,
                message: '认证状态检查失败'
            });
        }
    }
}

module.exports = AuthMiddleware;
