#!/usr/bin/env node

const bcrypt = require('bcryptjs');

/**
 * 简单的密码哈希生成工具
 */

function generateHash(password) {
    if (!password) {
        console.log('❌ 请提供密码');
        console.log('使用方法: node scripts/hash-password.js "your_password"');
        process.exit(1);
    }
    
    if (password.length < 6) {
        console.log('❌ 密码长度至少需要6个字符');
        process.exit(1);
    }
    
    console.log('🔐 正在生成密码哈希...');
    
    const saltRounds = 12;
    const hash = bcrypt.hashSync(password, saltRounds);
    
    console.log('');
    console.log('✅ 密码哈希生成成功！');
    console.log('');
    console.log('请将以下配置添加到您的 .env 文件中：');
    console.log('');
    console.log('# 管理员登录配置（安全版本）');
    console.log('ADMIN_USERNAME=admin');
    console.log(`ADMIN_PASSWORD_HASH=${hash}`);
    console.log('');
    console.log('⚠️  重要提醒：');
    console.log('1. 请删除 .env 文件中的 ADMIN_PASSWORD 配置');
    console.log('2. 请确保 .env 文件不会被提交到Git');
    console.log('3. 请妥善保管您的密码');
    console.log('');
}

// 获取命令行参数
const password = process.argv[2];

if (!password) {
    console.log('🔐 密码哈希生成工具');
    console.log('');
    console.log('使用方法:');
    console.log('  node scripts/hash-password.js "your_password"');
    console.log('');
    console.log('示例:');
    console.log('  node scripts/hash-password.js "mySecurePassword123"');
    console.log('');
    process.exit(0);
}

generateHash(password);
