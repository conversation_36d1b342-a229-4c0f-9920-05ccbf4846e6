[2025-07-07T12:28:31.766Z] [INFO] 内存数据库初始化完成（演示模式）
[2025-07-07T12:28:31.772Z] [INFO] 所有服务初始化完成
[2025-07-07T12:28:31.779Z] [INFO] 微信激活码机器人启动成功，端口: 3000
[2025-07-07T17:53:01.772Z] [ERROR] 数据库初始化失败: {
  "message": "Access denied for user 'root'@'localhost' (using password: NO)",
  "code": "ER_ACCESS_DENIED_ERROR",
  "errno": 1045,
  "sqlState": "28000"
}
[2025-07-07T17:53:01.776Z] [ERROR] 服务初始化失败: {
  "message": "Access denied for user 'root'@'localhost' (using password: NO)",
  "code": "ER_ACCESS_DENIED_ERROR",
  "errno": 1045,
  "sqlState": "28000"
}
