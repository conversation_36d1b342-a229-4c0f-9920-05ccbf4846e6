{"name": "@gradio/gallery", "version": "0.15.19", "description": "Gradio UI packages", "type": "module", "author": "", "license": "ISC", "private": false, "dependencies": {"@gradio/atoms": "workspace:^", "@gradio/client": "workspace:^", "@gradio/icons": "workspace:^", "@gradio/image": "workspace:^", "@gradio/statustracker": "workspace:^", "@gradio/upload": "workspace:^", "@gradio/utils": "workspace:^", "@gradio/video": "workspace:^", "@gradio/file": "workspace:^", "dequal": "^2.0.2"}, "devDependencies": {"@gradio/preview": "workspace:^"}, "main": "./Index.svelte", "main_changeset": true, "exports": {".": {"gradio": "./Index.svelte", "svelte": "./dist/Index.svelte", "types": "./dist/Index.svelte.d.ts"}, "./package.json": "./package.json", "./base": {"gradio": "./shared/Gallery.svelte", "svelte": "./dist/shared/Gallery.svelte", "types": "./dist/shared/Gallery.svelte.d.ts"}}, "peerDependencies": {"svelte": "^4.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/gradio-app/gradio.git", "directory": "js/gallery"}}