<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信激活码机器人</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .container {
            text-align: center;
            max-width: 600px;
            padding: 40px;
        }

        .logo {
            font-size: 80px;
            margin-bottom: 20px;
        }

        h1 {
            font-size: 48px;
            margin-bottom: 20px;
            font-weight: 300;
        }

        .subtitle {
            font-size: 20px;
            margin-bottom: 40px;
            opacity: 0.9;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .feature-icon {
            font-size: 40px;
            margin-bottom: 15px;
        }

        .feature h3 {
            font-size: 18px;
            margin-bottom: 10px;
        }

        .feature p {
            font-size: 14px;
            opacity: 0.8;
        }

        .btn-admin {
            display: inline-block;
            padding: 15px 30px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-size: 18px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.3);
            margin-top: 30px;
        }

        .btn-admin:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .footer {
            margin-top: 60px;
            opacity: 0.7;
            font-size: 14px;
        }

        .status {
            margin-top: 20px;
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            display: inline-block;
        }

        .status.online {
            background: rgba(46, 204, 113, 0.3);
        }

        .status.offline {
            background: rgba(231, 76, 60, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🤖</div>
        <h1>微信激活码机器人</h1>
        <p class="subtitle">智能化激活码分发系统</p>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">💬</div>
                <h3>微信集成</h3>
                <p>用户通过微信发送关键字即可获取激活码</p>
            </div>
            <div class="feature">
                <div class="feature-icon">🎫</div>
                <h3>激活码管理</h3>
                <p>支持批量生成、分配和统计激活码使用情况</p>
            </div>
            <div class="feature">
                <div class="feature-icon">📊</div>
                <h3>数据统计</h3>
                <p>实时查看激活码使用统计和用户活跃度</p>
            </div>
        </div>

        <div class="status" id="systemStatus">
            <span id="statusText">检查系统状态中...</span>
        </div>

        <a href="/login.html" class="btn-admin">进入管理后台</a>

        <div class="footer">
            <p>© 2025 微信激活码机器人 - 安全可靠的激活码分发解决方案</p>
        </div>
    </div>

    <script>
        // 检查系统状态
        async function checkSystemStatus() {
            try {
                const response = await fetch('/health');
                const data = await response.json();
                
                if (data.status === 'ok') {
                    document.getElementById('systemStatus').className = 'status online';
                    document.getElementById('statusText').textContent = '🟢 系统运行正常';
                } else {
                    throw new Error('系统状态异常');
                }
            } catch (error) {
                document.getElementById('systemStatus').className = 'status offline';
                document.getElementById('statusText').textContent = '🔴 系统离线';
            }
        }

        // 页面加载时检查状态
        document.addEventListener('DOMContentLoaded', checkSystemStatus);

        // 每30秒检查一次状态
        setInterval(checkSystemStatus, 30000);
    </script>
</body>
</html>
