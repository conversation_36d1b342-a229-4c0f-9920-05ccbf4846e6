<template>
  <view id="app">
    <!-- 全局加载提示 -->
    <view
      v-if="globalLoading"
      class="global-loading"
    >
      <text>正在加载...</text>
    </view>
  </view>
</template>

<script>
import { UserManager } from './utils/user.js'
import themeManager from './utils/theme.js'

export default {
  name: 'App',

  data() {
    return {
      globalLoading: false,
      themeClass: 'theme-auto',
      isNavigating: false, // 防止重复导航
      vipCheckTimer: null // VIP检查定时器
    }
  },

  onLaunch() {
    try {
      this.initApp()
      this.initTheme()
    } catch (error) {
      console.error('App Launch error:', error)
      // 即使初始化失败也要继续运行
    }
  },

  onShow() {
    // 每次应用显示时检查登录状态，但避免重复导航
    if (!this.isNavigating) {
      this.checkAuthAndRedirect()
    }

    // 检查VIP状态
    this.checkVipStatus()

    // 启动VIP状态定期检查
    this.startVipCheckTimer()

    // 监听用户信息更新事件
    uni.$on('userInfoUpdated', this.handleUserInfoUpdated)
  },

  onHide() {
    // 停止VIP检查定时器
    this.stopVipCheckTimer()
    // 移除事件监听
    uni.$off('userInfoUpdated', this.handleUserInfoUpdated)
  },

  methods: {
    // 初始化应用
    initApp() {
      try {
        // 延迟检查登录状态，避免阻塞启动
        setTimeout(() => {
          this.checkAuthAndRedirect()
        }, 500)

      } catch (error) {
        console.error('App initialization failed:', error)
        // 不显示错误提示，避免影响用户体验
      }
    },

    // 检查登录状态并重定向
    checkAuthAndRedirect() {
      try {
        // 如果正在导航中，避免重复操作
        if (this.isNavigating) {
          return
        }

        const isLoggedIn = UserManager.isLoggedIn()

        // 获取当前页面路径
        const pages = getCurrentPages()
        if (pages.length === 0) {
          return
        }

        const currentPage = pages[pages.length - 1]
        const currentRoute = currentPage ? currentPage.route : ''

        // 如果未登录且不在登录相关页面，跳转到登录页
        if (!isLoggedIn && !this.isAuthPage(currentRoute)) {
          this.navigateToLogin()
          return
        }

        // 如果已登录且在登录页面，跳转到首页
        if (isLoggedIn && this.isAuthPage(currentRoute)) {
          this.navigateToHome()
        }
      } catch (error) {
        console.error('检查登录状态失败:', error)
      }
    },

    // 处理用户信息更新事件
    handleUserInfoUpdated(data) {
      // 在App级别不需要特殊处理，主要由各个页面处理
    },

    // 检查VIP状态
    async checkVipStatus() {
      try {
        // 延迟执行，避免影响应用启动
        setTimeout(async () => {
          await UserManager.periodicVipCheck()
        }, 2000)
      } catch (error) {
        console.error('VIP状态检查失败:', error)
      }
    },

    // 启动VIP检查定时器
    startVipCheckTimer() {
      // 清除已存在的定时器
      this.stopVipCheckTimer()

      // 每2小时检查一次VIP状态（降低频率，减少不必要的API调用）
      this.vipCheckTimer = setInterval(async () => {
        try {
          await UserManager.periodicVipCheck()
        } catch (error) {
          console.error('定时VIP检查失败:', error)
        }
      }, 2 * 60 * 60 * 1000) // 2小时
    },

    // 停止VIP检查定时器
    stopVipCheckTimer() {
      if (this.vipCheckTimer) {
        clearInterval(this.vipCheckTimer)
        this.vipCheckTimer = null
      }
    },

    // 导航到登录页
    navigateToLogin() {
      if (this.isNavigating) return

      this.isNavigating = true
      uni.reLaunch({
        url: '/pages/login/login',
        complete: () => {
          setTimeout(() => {
            this.isNavigating = false
          }, 1000)
        }
      })
    },

    // 导航到首页
    navigateToHome() {
      if (this.isNavigating) return

      this.isNavigating = true
      uni.reLaunch({
        url: '/pages/index/index',
        complete: () => {
          setTimeout(() => {
            this.isNavigating = false
          }, 1000)
        }
      })
    },

    // 判断是否为登录相关页面
    isAuthPage(route) {
      const authPages = [
        'pages/login/login',
        'pages/user/register',
        'pages/user/forgot-password'
      ]
      return authPages.includes(route)
    },

    // 延迟函数
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    },

    // 初始化主题
    initTheme() {
      try {
        // 初始化主题管理器
        themeManager.init()

        // 监听系统主题变化
        themeManager.watchSystemTheme()

        // 设置全局主题类
        this.themeClass = `theme-${themeManager.getCurrentTheme()}`

        // 设置全局数据供其他页面使用
        this.globalData = this.globalData || {}
        this.globalData.themeManager = themeManager
        this.globalData.themeClass = this.themeClass

      } catch (error) {
        console.error('主题初始化失败:', error)
      }
    }
  }
}
</script>

<style lang="scss">
/* 导入主题样式 */
@import './styles/theme.scss';

/* 全局样式 */
page {
  background-color: var(--bg-color, #f8f9fa);
  color: var(--text-color, #333333);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 通用类 */
.text-center {
  text-align: center;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 颜色类 */
.text-primary {
  color: #2196F3;
}

.text-muted {
  color: #666;
}

/* 按钮样式 */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
}

.btn-primary {
  background: linear-gradient(135deg, #2196F3, #21CBF3);
  color: white;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

#app {
  height: 100vh;
  position: relative;
}

.global-loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;

  text {
    font-size: 28rpx;
    color: #2196F3;
  }
}
</style>
