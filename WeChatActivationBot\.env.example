# 微信公众号配置
WECHAT_TOKEN=your_wechat_token_here
WECHAT_APPID=wxd6ac8be433689bc8
WECHAT_APPSECRET=1a3ee40991854be644c3d5ff7405e2f4

# 服务器配置
PORT=3000
NODE_ENV=development

# MySQL数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=123456
DB_NAME=wechat_activation_bot

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# 激活码配置
DEFAULT_ACTIVATION_CODE_LENGTH=16
MAX_ACTIVATION_CODES_PER_KEYWORD=100

# 管理员登录配置
ADMIN_USERNAME=admin
# 安全方式：使用密码哈希（推荐生产环境）
# 使用 npm run generate-password-hash 生成
ADMIN_PASSWORD_HASH=your_bcrypt_hash_here

# 开发方式：明文密码（仅用于开发环境）
# ADMIN_PASSWORD=your_password_here

# JWT配置（生产环境请修改为随机字符串）
JWT_SECRET=your_jwt_secret_key_here_change_in_production
SESSION_TIMEOUT=24h
