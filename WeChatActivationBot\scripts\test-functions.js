#!/usr/bin/env node

const DatabaseService = require('../src/services/databaseService');
require('dotenv').config();

/**
 * 功能测试脚本
 * 测试微信自动回复机器人的核心功能
 */

async function testCoreFunctions() {
    console.log('🧪 开始测试微信自动回复机器人核心功能...\n');
    
    const db = new DatabaseService();
    
    try {
        // 初始化数据库连接
        await db.initialize();
        console.log('✅ 数据库连接成功\n');
        
        // 测试1：用户关注功能
        console.log('📝 测试1：用户关注功能');
        const testOpenId = 'test_function_user_001';
        const result1 = await db.handleUserSubscribe(testOpenId, '功能测试用户');
        console.log(`   关注结果: ${result1 ? '✅ 成功' : '❌ 失败'}`);
        
        // 测试2：检查用户是否已获取激活码
        console.log('\n📝 测试2：检查用户激活码状态');
        const hasUsed = await db.hasUserUsedActivationCode(testOpenId);
        console.log(`   激活码状态: ${hasUsed ? '已使用' : '未使用'} ✅`);
        
        // 测试3：关键词回复功能
        console.log('\n📝 测试3：关键词回复功能');
        const replyResult = await db.handleKeywordReply(testOpenId, 'emo');
        console.log(`   回复结果: ${replyResult.success ? '✅ 成功' : '❌ 失败'}`);
        console.log(`   回复内容: ${replyResult.message}`);
        
        // 测试4：重复获取激活码（应该被拒绝）
        console.log('\n📝 测试4：重复获取激活码测试');
        const replyResult2 = await db.handleKeywordReply(testOpenId, 'emo');
        console.log(`   重复获取结果: ${replyResult2.success ? '❌ 意外成功' : '✅ 正确拒绝'}`);
        console.log(`   拒绝消息: ${replyResult2.message}`);
        
        // 测试5：用户取消关注
        console.log('\n📝 测试5：用户取消关注功能');
        const result5 = await db.handleUserUnsubscribe(testOpenId);
        console.log(`   取消关注结果: ${result5 ? '✅ 成功' : '❌ 失败'}`);
        
        // 测试6：获取统计信息
        console.log('\n📝 测试6：统计信息功能');
        const userStats = await db.getUserStats();
        const activationStats = await db.getActivationCodeStats();
        console.log('   用户统计:', userStats);
        console.log('   激活码统计:', activationStats);
        
        // 测试7：获取用户列表
        console.log('\n📝 测试7：用户列表功能');
        const users = await db.getAllUsers(1, 5);
        console.log(`   用户列表: 获取到 ${users.length} 个用户 ✅`);
        
        // 测试8：关键词配置
        console.log('\n📝 测试8：关键词配置功能');
        const keywords = await db.getAllKeywords();
        console.log(`   关键词列表: 共 ${keywords.length} 个关键词 ✅`);
        keywords.forEach(kw => {
            console.log(`     - ${kw.keyword} (${kw.reply_type})`);
        });
        
        console.log('\n🎉 所有核心功能测试完成！');
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
    } finally {
        await db.close();
        console.log('\n🔒 数据库连接已关闭');
    }
}

// 微信消息模拟测试
async function testWeChatMessageFlow() {
    console.log('\n🤖 开始测试微信消息流程...\n');
    
    const db = new DatabaseService();
    
    try {
        await db.initialize();
        
        // 模拟微信消息对象
        const mockMessages = [
            {
                FromUserName: 'wx_test_user_001',
                ToUserName: 'gh_bot_account',
                MsgType: 'event',
                Event: 'subscribe'
            },
            {
                FromUserName: 'wx_test_user_001',
                ToUserName: 'gh_bot_account',
                MsgType: 'text',
                Content: 'emo'
            },
            {
                FromUserName: 'wx_test_user_001',
                ToUserName: 'gh_bot_account',
                MsgType: 'text',
                Content: 'hello'
            },
            {
                FromUserName: 'wx_test_user_001',
                ToUserName: 'gh_bot_account',
                MsgType: 'text',
                Content: 'unknown_keyword'
            }
        ];
        
        for (let i = 0; i < mockMessages.length; i++) {
            const msg = mockMessages[i];
            console.log(`📨 处理消息 ${i + 1}:`);
            console.log(`   类型: ${msg.MsgType}`);
            console.log(`   内容: ${msg.Content || msg.Event || '事件消息'}`);
            
            if (msg.MsgType === 'event' && msg.Event === 'subscribe') {
                const result = await db.handleUserSubscribe(msg.FromUserName, '微信测试用户');
                console.log(`   处理结果: ${result ? '✅ 关注成功' : '❌ 关注失败'}`);
            } else if (msg.MsgType === 'text') {
                const result = await db.handleKeywordReply(msg.FromUserName, msg.Content.toLowerCase());
                console.log(`   回复结果: ${result.success ? '✅ 成功' : '❌ 失败'}`);
                console.log(`   回复内容: ${result.message}`);
            }
            console.log('');
        }
        
    } catch (error) {
        console.error('❌ 微信消息流程测试失败:', error);
    } finally {
        await db.close();
    }
}

// 主函数
async function main() {
    const args = process.argv.slice(2);
    
    if (args.includes('--help') || args.includes('-h')) {
        console.log('🧪 微信自动回复机器人功能测试工具');
        console.log('');
        console.log('使用方法:');
        console.log('  npm run test-functions        # 测试所有核心功能');
        console.log('  npm run test-functions wechat # 测试微信消息流程');
        console.log('');
        return;
    }
    
    if (args.includes('wechat')) {
        await testWeChatMessageFlow();
    } else {
        await testCoreFunctions();
        await testWeChatMessageFlow();
    }
}

if (require.main === module) {
    main().catch(error => {
        console.error('测试执行失败:', error);
        process.exit(1);
    });
}

module.exports = { testCoreFunctions, testWeChatMessageFlow };
