#!/usr/bin/env node

const DatabaseService = require('../src/services/databaseService');
require('dotenv').config();

/**
 * 测试用户查询功能
 */

async function testUserQuery() {
    console.log('🔍 测试用户查询功能...\n');
    
    const db = new DatabaseService();
    
    try {
        await db.initialize();
        console.log('✅ 数据库连接成功\n');
        
        // 测试1：直接SQL查询
        console.log('📝 测试1：直接SQL查询');
        const [directRows] = await db.botConnection.execute('SELECT COUNT(*) as count FROM wechat_users');
        console.log('   直接查询用户总数:', directRows[0].count);
        
        const [directUsers] = await db.botConnection.execute('SELECT * FROM wechat_users LIMIT 5');
        console.log('   直接查询用户列表:', directUsers.length, '条记录');
        
        // 测试2：使用方法查询
        console.log('\n📝 测试2：使用getUserCount方法');
        const count = await db.getUserCount();
        console.log('   getUserCount结果:', count);
        
        console.log('\n📝 测试3：使用getAllUsers方法');
        const users = await db.getAllUsers(1, 20, '');
        console.log('   getAllUsers结果:', users.length, '条记录');
        
        if (users.length > 0) {
            console.log('   第一个用户:', {
                id: users[0].id,
                openid: users[0].openid,
                nickname: users[0].nickname
            });
        }
        
        // 测试4：测试分页参数
        console.log('\n📝 测试4：测试不同分页参数');
        const users2 = await db.getAllUsers('1', '20', '');
        console.log('   字符串参数结果:', users2.length, '条记录');
        
        const users3 = await db.getAllUsers(1, 5, '');
        console.log('   限制5条记录结果:', users3.length, '条记录');
        
        // 测试5：测试搜索功能
        console.log('\n📝 测试5：测试搜索功能');
        const searchUsers = await db.getAllUsers(1, 20, 'test');
        console.log('   搜索"test"结果:', searchUsers.length, '条记录');
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
    } finally {
        await db.close();
        console.log('\n🔒 数据库连接已关闭');
    }
}

if (require.main === module) {
    testUserQuery().catch(error => {
        console.error('测试执行失败:', error);
        process.exit(1);
    });
}

module.exports = { testUserQuery };
