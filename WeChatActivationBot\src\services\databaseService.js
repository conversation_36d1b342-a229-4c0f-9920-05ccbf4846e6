const mysql = require('mysql2/promise');
const Logger = require('../utils/logger');

class DatabaseService {
    constructor() {
        this.connection = null;
        this.config = {
            host: process.env.DB_HOST || 'localhost',
            port: process.env.DB_PORT || 3306,
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || '',
            database: process.env.DB_NAME || 'wechat_activation_bot',
            charset: 'utf8mb4'
        };
    }

    /**
     * 初始化数据库连接
     */
    async initialize() {
        try {
            // 先连接到MySQL服务器（不指定数据库）
            const tempConnection = await mysql.createConnection({
                host: this.config.host,
                port: this.config.port,
                user: this.config.user,
                password: this.config.password
            });

            // 创建数据库（如果不存在）
            await tempConnection.execute(`CREATE DATABASE IF NOT EXISTS \`${this.config.database}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
            await tempConnection.end();

            // 连接到指定数据库
            this.connection = await mysql.createConnection(this.config);
            
            // 创建表
            await this.createTables();
            
            Logger.info('MySQL数据库初始化完成');
        } catch (error) {
            Logger.error('数据库初始化失败:', error);
            throw error;
        }
    }

    /**
     * 创建数据库表
     */
    async createTables() {
        const tables = [
            // 关键字表
            `CREATE TABLE IF NOT EXISTS keywords (
                id INT AUTO_INCREMENT PRIMARY KEY,
                keyword VARCHAR(255) UNIQUE NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_keyword (keyword)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,
            
            // 激活码表
            `CREATE TABLE IF NOT EXISTS activation_codes (
                id INT AUTO_INCREMENT PRIMARY KEY,
                keyword_id INT NOT NULL,
                code VARCHAR(255) UNIQUE NOT NULL,
                used_by VARCHAR(255) NULL,
                used_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (keyword_id) REFERENCES keywords (id) ON DELETE CASCADE,
                INDEX idx_keyword_id (keyword_id),
                INDEX idx_code (code),
                INDEX idx_used_by (used_by)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,
            
            // 用户使用记录表
            `CREATE TABLE IF NOT EXISTS user_usage_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id VARCHAR(255) NOT NULL,
                keyword_id INT NOT NULL,
                activation_code_id INT NOT NULL,
                request_content TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (keyword_id) REFERENCES keywords (id),
                FOREIGN KEY (activation_code_id) REFERENCES activation_codes (id),
                INDEX idx_user_id (user_id),
                INDEX idx_keyword_id (keyword_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`
        ];

        for (const sql of tables) {
            await this.connection.execute(sql);
        }
    }

    // ==================== 关键字相关方法 ====================

    async getAllKeywords() {
        const [rows] = await this.connection.execute('SELECT * FROM keywords ORDER BY created_at DESC');
        return rows;
    }

    async getKeywordById(id) {
        const [rows] = await this.connection.execute('SELECT * FROM keywords WHERE id = ?', [id]);
        return rows[0];
    }

    async addKeyword(keyword, description = '') {
        const [result] = await this.connection.execute(
            'INSERT INTO keywords (keyword, description) VALUES (?, ?)', 
            [keyword, description]
        );
        return await this.getKeywordById(result.insertId);
    }

    async removeKeyword(id) {
        const [result] = await this.connection.execute('DELETE FROM keywords WHERE id = ?', [id]);
        return result;
    }

    // ==================== 激活码相关方法 ====================

    async getAvailableActivationCode(keywordId) {
        const [rows] = await this.connection.execute(`
            SELECT * FROM activation_codes 
            WHERE keyword_id = ? AND used_by IS NULL 
            ORDER BY created_at ASC 
            LIMIT 1
        `, [keywordId]);
        return rows[0];
    }

    async assignActivationCode(codeId, userId) {
        const [result] = await this.connection.execute(
            'UPDATE activation_codes SET used_by = ?, used_at = NOW() WHERE id = ?', 
            [userId, codeId]
        );
        return result;
    }

    async getUserActivationCode(keywordId, userId) {
        const [rows] = await this.connection.execute(`
            SELECT * FROM activation_codes 
            WHERE keyword_id = ? AND used_by = ?
        `, [keywordId, userId]);
        return rows[0];
    }

    async batchInsertActivationCodes(keywordId, codes) {
        if (codes.length === 0) return [];
        
        const values = codes.map(code => [keywordId, code]);
        const placeholders = codes.map(() => '(?, ?)').join(', ');
        const flatValues = values.flat();
        
        const [result] = await this.connection.execute(
            `INSERT INTO activation_codes (keyword_id, code) VALUES ${placeholders}`,
            flatValues
        );
        
        // 返回插入的记录
        const results = [];
        for (let i = 0; i < codes.length; i++) {
            results.push({
                id: result.insertId + i,
                code: codes[i],
                keyword_id: keywordId
            });
        }
        return results;
    }

    async getActivationCodesByKeyword(keywordId) {
        const [rows] = await this.connection.execute(
            'SELECT * FROM activation_codes WHERE keyword_id = ? ORDER BY created_at DESC', 
            [keywordId]
        );
        return rows;
    }

    async getActivationCodeByCode(code) {
        const [rows] = await this.connection.execute('SELECT * FROM activation_codes WHERE code = ?', [code]);
        return rows[0];
    }

    async addActivationCode(keywordId, code) {
        const [result] = await this.connection.execute(
            'INSERT INTO activation_codes (keyword_id, code) VALUES (?, ?)', 
            [keywordId, code]
        );
        return { id: result.insertId, keyword_id: keywordId, code };
    }

    async removeActivationCode(id) {
        const [result] = await this.connection.execute('DELETE FROM activation_codes WHERE id = ?', [id]);
        return result;
    }

    async getActivationCodeStats(keywordId) {
        const [rows] = await this.connection.execute(`
            SELECT 
                COUNT(*) as total,
                COUNT(used_by) as used
            FROM activation_codes 
            WHERE keyword_id = ?
        `, [keywordId]);
        return rows[0];
    }

    async resetActivationCodes(keywordId) {
        const [result] = await this.connection.execute(
            'UPDATE activation_codes SET used_by = NULL, used_at = NULL WHERE keyword_id = ?', 
            [keywordId]
        );
        return result.affectedRows;
    }

    // ==================== 使用记录相关方法 ====================

    async logUserUsage(userId, keywordId, activationCodeId, requestContent) {
        const [result] = await this.connection.execute(`
            INSERT INTO user_usage_logs (user_id, keyword_id, activation_code_id, request_content) 
            VALUES (?, ?, ?, ?)
        `, [userId, keywordId, activationCodeId, requestContent]);
        return result;
    }

    async getUserUsageHistory(userId, limit = 50) {
        const [rows] = await this.connection.execute(`
            SELECT 
                uul.*,
                k.keyword,
                ac.code
            FROM user_usage_logs uul
            JOIN keywords k ON uul.keyword_id = k.id
            JOIN activation_codes ac ON uul.activation_code_id = ac.id
            WHERE uul.user_id = ?
            ORDER BY uul.created_at DESC
            LIMIT ?
        `, [userId, limit]);
        return rows;
    }

    /**
     * 关闭数据库连接
     */
    async close() {
        if (this.connection) {
            await this.connection.end();
            Logger.info('MySQL数据库连接已关闭');
        }
    }

    /**
     * 测试数据库连接
     */
    async testConnection() {
        try {
            await this.connection.ping();
            return true;
        } catch (error) {
            Logger.error('数据库连接测试失败:', error);
            return false;
        }
    }
}

module.exports = DatabaseService;
