const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const Logger = require('../utils/logger');

class DatabaseService {
    constructor() {
        this.dbPath = process.env.DB_PATH || './data/activation_bot.db';
        this.db = null;
    }

    /**
     * 初始化数据库
     */
    async initialize() {
        try {
            // 确保数据目录存在
            const dbDir = path.dirname(this.dbPath);
            if (!fs.existsSync(dbDir)) {
                fs.mkdirSync(dbDir, { recursive: true });
            }

            // 连接数据库
            this.db = new sqlite3.Database(this.dbPath);
            
            // 创建表
            await this.createTables();
            
            Logger.info('数据库初始化完成');
        } catch (error) {
            Logger.error('数据库初始化失败:', error);
            throw error;
        }
    }

    /**
     * 创建数据库表
     */
    async createTables() {
        const tables = [
            // 关键字表
            `CREATE TABLE IF NOT EXISTS keywords (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                keyword TEXT UNIQUE NOT NULL,
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,
            
            // 激活码表
            `CREATE TABLE IF NOT EXISTS activation_codes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                keyword_id INTEGER NOT NULL,
                code TEXT UNIQUE NOT NULL,
                used_by TEXT,
                used_at DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (keyword_id) REFERENCES keywords (id) ON DELETE CASCADE
            )`,
            
            // 用户使用记录表
            `CREATE TABLE IF NOT EXISTS user_usage_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                keyword_id INTEGER NOT NULL,
                activation_code_id INTEGER NOT NULL,
                request_content TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (keyword_id) REFERENCES keywords (id),
                FOREIGN KEY (activation_code_id) REFERENCES activation_codes (id)
            )`,
            
            // 系统配置表
            `CREATE TABLE IF NOT EXISTS system_config (
                key TEXT PRIMARY KEY,
                value TEXT,
                description TEXT,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`
        ];

        for (const sql of tables) {
            await this.runQuery(sql);
        }

        // 创建索引
        const indexes = [
            'CREATE INDEX IF NOT EXISTS idx_keywords_keyword ON keywords(keyword)',
            'CREATE INDEX IF NOT EXISTS idx_activation_codes_keyword_id ON activation_codes(keyword_id)',
            'CREATE INDEX IF NOT EXISTS idx_activation_codes_code ON activation_codes(code)',
            'CREATE INDEX IF NOT EXISTS idx_activation_codes_used_by ON activation_codes(used_by)',
            'CREATE INDEX IF NOT EXISTS idx_user_usage_logs_user_id ON user_usage_logs(user_id)',
            'CREATE INDEX IF NOT EXISTS idx_user_usage_logs_keyword_id ON user_usage_logs(keyword_id)'
        ];

        for (const sql of indexes) {
            await this.runQuery(sql);
        }
    }

    /**
     * 执行SQL查询
     */
    runQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.run(sql, params, function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ id: this.lastID, changes: this.changes });
                }
            });
        });
    }

    /**
     * 执行SQL查询并返回单行结果
     */
    getQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    /**
     * 执行SQL查询并返回所有结果
     */
    allQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    // ==================== 关键字相关方法 ====================

    /**
     * 获取所有关键字
     */
    async getAllKeywords() {
        const sql = 'SELECT * FROM keywords ORDER BY created_at DESC';
        return await this.allQuery(sql);
    }

    /**
     * 根据ID获取关键字
     */
    async getKeywordById(id) {
        const sql = 'SELECT * FROM keywords WHERE id = ?';
        return await this.getQuery(sql, [id]);
    }

    /**
     * 添加关键字
     */
    async addKeyword(keyword, description = '') {
        const sql = 'INSERT INTO keywords (keyword, description) VALUES (?, ?)';
        const result = await this.runQuery(sql, [keyword, description]);
        return await this.getKeywordById(result.id);
    }

    /**
     * 删除关键字
     */
    async removeKeyword(id) {
        const sql = 'DELETE FROM keywords WHERE id = ?';
        return await this.runQuery(sql, [id]);
    }

    // ==================== 激活码相关方法 ====================

    /**
     * 获取可用的激活码
     */
    async getAvailableActivationCode(keywordId) {
        const sql = `
            SELECT * FROM activation_codes 
            WHERE keyword_id = ? AND used_by IS NULL 
            ORDER BY created_at ASC 
            LIMIT 1
        `;
        return await this.getQuery(sql, [keywordId]);
    }

    /**
     * 分配激活码给用户
     */
    async assignActivationCode(codeId, userId) {
        const sql = 'UPDATE activation_codes SET used_by = ?, used_at = CURRENT_TIMESTAMP WHERE id = ?';
        return await this.runQuery(sql, [userId, codeId]);
    }

    /**
     * 获取用户已获取的激活码
     */
    async getUserActivationCode(keywordId, userId) {
        const sql = `
            SELECT * FROM activation_codes 
            WHERE keyword_id = ? AND used_by = ?
        `;
        return await this.getQuery(sql, [keywordId, userId]);
    }

    /**
     * 批量插入激活码
     */
    async batchInsertActivationCodes(keywordId, codes) {
        const sql = 'INSERT INTO activation_codes (keyword_id, code) VALUES (?, ?)';
        const results = [];
        
        for (const code of codes) {
            const result = await this.runQuery(sql, [keywordId, code]);
            results.push({ id: result.id, code, keyword_id: keywordId });
        }
        
        return results;
    }

    /**
     * 获取关键字的所有激活码
     */
    async getActivationCodesByKeyword(keywordId) {
        const sql = 'SELECT * FROM activation_codes WHERE keyword_id = ? ORDER BY created_at DESC';
        return await this.allQuery(sql, [keywordId]);
    }

    /**
     * 根据激活码获取记录
     */
    async getActivationCodeByCode(code) {
        const sql = 'SELECT * FROM activation_codes WHERE code = ?';
        return await this.getQuery(sql, [code]);
    }

    /**
     * 添加单个激活码
     */
    async addActivationCode(keywordId, code) {
        const sql = 'INSERT INTO activation_codes (keyword_id, code) VALUES (?, ?)';
        const result = await this.runQuery(sql, [keywordId, code]);
        return { id: result.id, keyword_id: keywordId, code };
    }

    /**
     * 删除激活码
     */
    async removeActivationCode(id) {
        const sql = 'DELETE FROM activation_codes WHERE id = ?';
        return await this.runQuery(sql, [id]);
    }

    /**
     * 获取激活码统计信息
     */
    async getActivationCodeStats(keywordId) {
        const sql = `
            SELECT 
                COUNT(*) as total,
                COUNT(used_by) as used
            FROM activation_codes 
            WHERE keyword_id = ?
        `;
        return await this.getQuery(sql, [keywordId]);
    }

    /**
     * 重置激活码
     */
    async resetActivationCodes(keywordId) {
        const sql = 'UPDATE activation_codes SET used_by = NULL, used_at = NULL WHERE keyword_id = ?';
        const result = await this.runQuery(sql, [keywordId]);
        return result.changes;
    }

    // ==================== 使用记录相关方法 ====================

    /**
     * 记录用户使用日志
     */
    async logUserUsage(userId, keywordId, activationCodeId, requestContent) {
        const sql = `
            INSERT INTO user_usage_logs (user_id, keyword_id, activation_code_id, request_content) 
            VALUES (?, ?, ?, ?)
        `;
        return await this.runQuery(sql, [userId, keywordId, activationCodeId, requestContent]);
    }

    /**
     * 获取用户使用历史
     */
    async getUserUsageHistory(userId, limit = 50) {
        const sql = `
            SELECT 
                uul.*,
                k.keyword,
                ac.code
            FROM user_usage_logs uul
            JOIN keywords k ON uul.keyword_id = k.id
            JOIN activation_codes ac ON uul.activation_code_id = ac.id
            WHERE uul.user_id = ?
            ORDER BY uul.created_at DESC
            LIMIT ?
        `;
        return await this.allQuery(sql, [userId, limit]);
    }

    /**
     * 关闭数据库连接
     */
    close() {
        if (this.db) {
            this.db.close();
            Logger.info('数据库连接已关闭');
        }
    }
}

module.exports = DatabaseService;
