const mysql = require('mysql2/promise');
const Logger = require('../utils/logger');

class DatabaseService {
    constructor() {
        this.connection = null;
        this.config = {
            host: process.env.DB_HOST || 'localhost',
            port: process.env.DB_PORT || 3306,
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || '',
            database: process.env.DB_NAME || 'emotional_reply_db',
            charset: 'utf8mb4'
        };
    }

    /**
     * 初始化数据库连接
     */
    async initialize() {
        try {
            // 连接到现有数据库
            this.connection = await mysql.createConnection(this.config);
            
            // 检查并创建缺失的表或字段
            await this.ensureTablesExist();
            
            Logger.info(`已连接到现有数据库: ${this.config.database}`);
        } catch (error) {
            Logger.error('数据库连接失败:', error);
            throw error;
        }
    }

    /**
     * 确保必要的表和字段存在
     */
    async ensureTablesExist() {
        try {
            // 检查activation_codes表是否存在
            const [tables] = await this.connection.execute(`
                SELECT TABLE_NAME 
                FROM information_schema.TABLES 
                WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'activation_codes'
            `, [this.config.database]);

            if (tables.length === 0) {
                // 如果表不存在，创建它
                await this.connection.execute(`
                    CREATE TABLE activation_codes (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        keyword_id INT,
                        code VARCHAR(255) UNIQUE NOT NULL,
                        used_by VARCHAR(255) NULL,
                        used_at TIMESTAMP NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        INDEX idx_code (code),
                        INDEX idx_used_by (used_by)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                `);
                Logger.info('创建了activation_codes表');
            }

            // 检查是否需要创建keywords表（如果不存在）
            const [keywordTables] = await this.connection.execute(`
                SELECT TABLE_NAME 
                FROM information_schema.TABLES 
                WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'keywords'
            `, [this.config.database]);

            if (keywordTables.length === 0) {
                await this.connection.execute(`
                    CREATE TABLE keywords (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        keyword VARCHAR(255) UNIQUE NOT NULL,
                        description TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        INDEX idx_keyword (keyword)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                `);
                Logger.info('创建了keywords表');
            }

        } catch (error) {
            Logger.error('检查表结构失败:', error);
            // 不抛出错误，继续使用现有结构
        }
    }

    // ==================== 关键字相关方法 ====================

    async getAllKeywords() {
        try {
            const [rows] = await this.connection.execute('SELECT * FROM keywords ORDER BY created_at DESC');
            return rows;
        } catch (error) {
            // 如果keywords表不存在，返回空数组
            Logger.warn('keywords表可能不存在，返回空列表');
            return [];
        }
    }

    async getKeywordById(id) {
        try {
            const [rows] = await this.connection.execute('SELECT * FROM keywords WHERE id = ?', [id]);
            return rows[0];
        } catch (error) {
            Logger.error('获取关键字失败:', error);
            return null;
        }
    }

    async addKeyword(keyword, description = '') {
        try {
            const [result] = await this.connection.execute(
                'INSERT INTO keywords (keyword, description) VALUES (?, ?)', 
                [keyword, description]
            );
            return await this.getKeywordById(result.insertId);
        } catch (error) {
            Logger.error('添加关键字失败:', error);
            throw error;
        }
    }

    async removeKeyword(id) {
        try {
            const [result] = await this.connection.execute('DELETE FROM keywords WHERE id = ?', [id]);
            return result;
        } catch (error) {
            Logger.error('删除关键字失败:', error);
            throw error;
        }
    }

    // ==================== 激活码相关方法 ====================

    async getAvailableActivationCode(keywordId) {
        try {
            const [rows] = await this.connection.execute(`
                SELECT * FROM activation_codes 
                WHERE (keyword_id = ? OR keyword_id IS NULL) AND used_by IS NULL 
                ORDER BY created_at ASC 
                LIMIT 1
            `, [keywordId]);
            return rows[0];
        } catch (error) {
            Logger.error('获取可用激活码失败:', error);
            return null;
        }
    }

    async assignActivationCode(codeId, userId) {
        try {
            const [result] = await this.connection.execute(
                'UPDATE activation_codes SET used_by = ?, used_at = NOW() WHERE id = ?', 
                [userId, codeId]
            );
            return result;
        } catch (error) {
            Logger.error('分配激活码失败:', error);
            throw error;
        }
    }

    async getUserActivationCode(keywordId, userId) {
        try {
            const [rows] = await this.connection.execute(`
                SELECT * FROM activation_codes 
                WHERE (keyword_id = ? OR keyword_id IS NULL) AND used_by = ?
            `, [keywordId, userId]);
            return rows[0];
        } catch (error) {
            Logger.error('获取用户激活码失败:', error);
            return null;
        }
    }

    async batchInsertActivationCodes(keywordId, codes) {
        if (codes.length === 0) return [];
        
        try {
            const values = codes.map(code => [keywordId, code]);
            const placeholders = codes.map(() => '(?, ?)').join(', ');
            const flatValues = values.flat();
            
            const [result] = await this.connection.execute(
                `INSERT INTO activation_codes (keyword_id, code) VALUES ${placeholders}`,
                flatValues
            );
            
            const results = [];
            for (let i = 0; i < codes.length; i++) {
                results.push({
                    id: result.insertId + i,
                    code: codes[i],
                    keyword_id: keywordId
                });
            }
            return results;
        } catch (error) {
            Logger.error('批量插入激活码失败:', error);
            throw error;
        }
    }

    async getActivationCodesByKeyword(keywordId) {
        try {
            const [rows] = await this.connection.execute(
                'SELECT * FROM activation_codes WHERE keyword_id = ? ORDER BY created_at DESC', 
                [keywordId]
            );
            return rows;
        } catch (error) {
            Logger.error('获取关键字激活码失败:', error);
            return [];
        }
    }

    async getAllActivationCodes() {
        try {
            const [rows] = await this.connection.execute(
                'SELECT * FROM activation_codes ORDER BY created_at DESC'
            );
            return rows;
        } catch (error) {
            Logger.error('获取所有激活码失败:', error);
            return [];
        }
    }

    async getActivationCodeByCode(code) {
        try {
            const [rows] = await this.connection.execute('SELECT * FROM activation_codes WHERE code = ?', [code]);
            return rows[0];
        } catch (error) {
            Logger.error('根据代码获取激活码失败:', error);
            return null;
        }
    }

    async addActivationCode(keywordId, code) {
        try {
            const [result] = await this.connection.execute(
                'INSERT INTO activation_codes (keyword_id, code) VALUES (?, ?)', 
                [keywordId, code]
            );
            return { id: result.insertId, keyword_id: keywordId, code };
        } catch (error) {
            Logger.error('添加激活码失败:', error);
            throw error;
        }
    }

    async removeActivationCode(id) {
        try {
            const [result] = await this.connection.execute('DELETE FROM activation_codes WHERE id = ?', [id]);
            return result;
        } catch (error) {
            Logger.error('删除激活码失败:', error);
            throw error;
        }
    }

    async getActivationCodeStats(keywordId) {
        try {
            const [rows] = await this.connection.execute(`
                SELECT 
                    COUNT(*) as total,
                    COUNT(used_by) as used
                FROM activation_codes 
                WHERE keyword_id = ? OR (keyword_id IS NULL AND ? IS NULL)
            `, [keywordId, keywordId]);
            return rows[0];
        } catch (error) {
            Logger.error('获取激活码统计失败:', error);
            return { total: 0, used: 0 };
        }
    }

    async resetActivationCodes(keywordId) {
        try {
            const [result] = await this.connection.execute(
                'UPDATE activation_codes SET used_by = NULL, used_at = NULL WHERE keyword_id = ?', 
                [keywordId]
            );
            return result.affectedRows;
        } catch (error) {
            Logger.error('重置激活码失败:', error);
            throw error;
        }
    }

    /**
     * 关闭数据库连接
     */
    async close() {
        if (this.connection) {
            await this.connection.end();
            Logger.info('数据库连接已关闭');
        }
    }

    /**
     * 测试数据库连接
     */
    async testConnection() {
        try {
            await this.connection.ping();
            return true;
        } catch (error) {
            Logger.error('数据库连接测试失败:', error);
            return false;
        }
    }
}

module.exports = DatabaseService;
