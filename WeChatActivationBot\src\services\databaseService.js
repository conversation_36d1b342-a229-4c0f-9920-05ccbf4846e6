const mysql = require('mysql2/promise');
const Logger = require('../utils/logger');

class DatabaseService {
    constructor() {
        this.activationConnection = null;  // 外部激活码数据库连接
        this.botConnection = null;         // 机器人数据库连接

        this.baseConfig = {
            host: process.env.DB_HOST || 'localhost',
            port: process.env.DB_PORT || 3306,
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || '',
            charset: 'utf8mb4'
        };

        this.activationDbName = process.env.ACTIVATION_DB_NAME || 'emotional_reply_db';
        this.botDbName = process.env.BOT_DB_NAME || 'wechat_reply_bot';
    }

    /**
     * 初始化数据库连接
     */
    async initialize() {
        try {
            Logger.info('开始初始化数据库连接...');

            // 连接到激活码数据库
            this.activationConnection = await mysql.createConnection({
                ...this.baseConfig,
                database: this.activationDbName
            });
            Logger.info(`✅ 已连接到激活码数据库: ${this.activationDbName}`);

            // 连接到机器人数据库
            if (this.botDbName !== this.activationDbName) {
                // 先检查机器人数据库是否存在，不存在则创建
                const tempConnection = await mysql.createConnection(this.baseConfig);
                await tempConnection.execute(`CREATE DATABASE IF NOT EXISTS \`${this.botDbName}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
                await tempConnection.end();

                this.botConnection = await mysql.createConnection({
                    ...this.baseConfig,
                    database: this.botDbName
                });
                Logger.info(`✅ 已连接到机器人数据库: ${this.botDbName}`);
            } else {
                // 如果是同一个数据库，使用相同的连接
                this.botConnection = this.activationConnection;
                Logger.info('✅ 激活码和机器人使用同一个数据库');
            }

            // 确保机器人表存在
            await this.ensureBotTablesExist();

            // 检查数据库版本并执行必要的初始化
            await this.checkDatabaseVersion();
            
            // 检查激活码表结构
            await this.checkActivationCodeTable();
            
        } catch (error) {
            Logger.error('数据库初始化失败:', error);
            throw error;
        }
    }

    /**
     * 确保机器人表存在
     */
    async ensureBotTablesExist() {
        try {
            // 用户表 - 记录关注用户和激活码使用情况
            await this.botConnection.execute(`
                CREATE TABLE IF NOT EXISTS wechat_users (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY,
                    openid VARCHAR(64) UNIQUE NOT NULL COMMENT '微信用户openid',
                    nickname VARCHAR(255) DEFAULT NULL COMMENT '用户昵称',
                    is_subscribed TINYINT DEFAULT 1 COMMENT '是否关注：1-关注，0-未关注',
                    subscribe_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '关注时间',
                    unsubscribe_time DATETIME DEFAULT NULL COMMENT '取消关注时间',
                    activation_code_used VARCHAR(32) DEFAULT NULL COMMENT '已使用的激活码',
                    activation_code_time DATETIME DEFAULT NULL COMMENT '激活码使用时间',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_openid (openid),
                    INDEX idx_subscribed (is_subscribed)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            `);

            // 关键词表 - 动态配置关键词和回复内容
            await this.botConnection.execute(`
                CREATE TABLE IF NOT EXISTS reply_keywords (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    keyword VARCHAR(255) NOT NULL COMMENT '关键词',
                    reply_type ENUM('activation_code', 'text') DEFAULT 'text' COMMENT '回复类型',
                    reply_content TEXT COMMENT '回复内容模板',
                    activation_code_type VARCHAR(20) DEFAULT NULL COMMENT '激活码类型：vip_1d等',
                    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_keyword (keyword),
                    INDEX idx_active (is_active)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            `);

            // 系统配置表 - 用于版本管理和配置存储
            await this.botConnection.execute(`
                CREATE TABLE IF NOT EXISTS system_config (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
                    config_value TEXT COMMENT '配置值',
                    description VARCHAR(255) COMMENT '配置描述',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_key (config_key)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            `);

            Logger.info('机器人表检查完成');
        } catch (error) {
            Logger.error('创建机器人表失败:', error);
        }
    }

    /**
     * 初始化默认关键词
     */
    async initializeDefaultKeywords() {
        try {
            // 检查是否已存在emo关键词
            const [existing] = await this.botConnection.execute(`
                SELECT id FROM reply_keywords WHERE keyword = 'emo'
            `);

            if (existing.length === 0) {
                // 不存在则插入默认关键词
                await this.botConnection.execute(`
                    INSERT INTO reply_keywords (keyword, reply_type, reply_content, activation_code_type)
                    VALUES ('emo', 'activation_code', '🎉 恭喜您获得1天VIP激活码：{code}\\n\\n请妥善保管，激活码仅可使用一次！', 'vip_1d')
                `);
                Logger.info('已初始化默认关键词: emo');
            } else {
                Logger.info('默认关键词已存在，跳过初始化');
            }
        } catch (error) {
            Logger.error('初始化默认关键词失败:', error);
        }
    }

    /**
     * 检查数据库版本并执行初始化
     */
    async checkDatabaseVersion() {
        try {
            const currentVersion = '1.0.0';

            // 检查当前数据库版本
            const [versionRows] = await this.botConnection.execute(`
                SELECT config_value FROM system_config WHERE config_key = 'db_version'
            `);

            if (versionRows.length === 0) {
                // 首次初始化
                Logger.info('首次初始化数据库...');

                // 插入版本信息
                await this.botConnection.execute(`
                    INSERT INTO system_config (config_key, config_value, description)
                    VALUES ('db_version', ?, '数据库版本号')
                `, [currentVersion]);

                // 初始化默认数据
                await this.initializeDefaultKeywords();

                Logger.info(`数据库初始化完成，版本: ${currentVersion}`);
            } else {
                const dbVersion = versionRows[0].config_value;
                Logger.info(`数据库版本: ${dbVersion}`);

                // 这里可以添加版本升级逻辑
                if (dbVersion !== currentVersion) {
                    Logger.info(`数据库版本不匹配，当前: ${dbVersion}, 期望: ${currentVersion}`);
                    // 可以在这里添加数据库升级逻辑
                }
            }
        } catch (error) {
            Logger.error('检查数据库版本失败:', error);
        }
    }

    /**
     * 检查激活码表结构
     */
    async checkActivationCodeTable() {
        try {
            const [columns] = await this.activationConnection.execute(`
                SELECT COLUMN_NAME 
                FROM information_schema.COLUMNS 
                WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'activation_codes'
            `, [this.activationDbName]);
            
            const columnNames = columns.map(col => col.COLUMN_NAME);
            Logger.info('激活码表字段:', columnNames);
            
            // 检查总数
            const [count] = await this.activationConnection.execute('SELECT COUNT(*) as total FROM activation_codes');
            Logger.info(`激活码表中共有 ${count[0].total} 条记录`);
            
        } catch (error) {
            Logger.error('检查激活码表失败:', error);
        }
    }

    // ==================== 微信用户相关方法 ====================

    /**
     * 用户关注事件
     */
    async handleUserSubscribe(openid, nickname = null) {
        try {
            await this.botConnection.execute(`
                INSERT INTO wechat_users (openid, nickname, is_subscribed, subscribe_time)
                VALUES (?, ?, 1, NOW())
                ON DUPLICATE KEY UPDATE
                is_subscribed = 1,
                subscribe_time = NOW(),
                nickname = COALESCE(?, nickname)
            `, [openid, nickname, nickname]);

            Logger.info(`用户关注: ${openid}`);
            return true;
        } catch (error) {
            Logger.error('处理用户关注失败:', error);
            return false;
        }
    }

    /**
     * 用户取消关注事件
     */
    async handleUserUnsubscribe(openid) {
        try {
            await this.botConnection.execute(`
                UPDATE wechat_users
                SET is_subscribed = 0, unsubscribe_time = NOW()
                WHERE openid = ?
            `, [openid]);

            Logger.info(`用户取消关注: ${openid}`);
            return true;
        } catch (error) {
            Logger.error('处理用户取消关注失败:', error);
            return false;
        }
    }

    /**
     * 检查用户是否已经获取过激活码
     */
    async hasUserUsedActivationCode(openid) {
        try {
            const [rows] = await this.botConnection.execute(`
                SELECT activation_code_used
                FROM wechat_users
                WHERE openid = ? AND activation_code_used IS NOT NULL
            `, [openid]);

            return rows.length > 0;
        } catch (error) {
            Logger.error('检查用户激活码使用状态失败:', error);
            return true; // 出错时返回true，避免重复发放
        }
    }

    async getKeywordById(id) {
        try {
            const [rows] = await this.keywordConnection.execute('SELECT * FROM keywords WHERE id = ?', [id]);
            const keyword = rows[0];
            if (keyword) {
                keyword.activation_code_stats = await this.getActivationCodeStats(id);
            }
            return keyword;
        } catch (error) {
            Logger.error('获取关键字失败:', error);
            return null;
        }
    }

    async addKeyword(keyword, description = '') {
        try {
            const [result] = await this.keywordConnection.execute(
                'INSERT INTO keywords (keyword, description) VALUES (?, ?)', 
                [keyword, description]
            );
            return await this.getKeywordById(result.insertId);
        } catch (error) {
            Logger.error('添加关键字失败:', error);
            throw error;
        }
    }

    async removeKeyword(id) {
        try {
            const [result] = await this.keywordConnection.execute('DELETE FROM keywords WHERE id = ?', [id]);
            return result;
        } catch (error) {
            Logger.error('删除关键字失败:', error);
            throw error;
        }
    }

    // ==================== 激活码相关方法 ====================

    /**
     * 获取指定类型的可用激活码
     */
    async getAvailableActivationCode(codeType = 'vip_1d') {
        try {
            const [rows] = await this.activationConnection.execute(`
                SELECT * FROM activation_codes
                WHERE code_type = ? AND status = 0 AND used_by IS NULL
                ORDER BY created_time ASC
                LIMIT 1
            `, [codeType]);

            return rows[0];
        } catch (error) {
            Logger.error('获取可用激活码失败:', error);
            return null;
        }
    }

    /**
     * 标记激活码为已使用
     */
    async markActivationCodeAsUsed(codeId, openid) {
        try {
            // 更新外部激活码数据库
            await this.activationConnection.execute(`
                UPDATE activation_codes
                SET used_by = ?, used_time = NOW(), status = 1
                WHERE id = ?
            `, [openid, codeId]);

            return true;
        } catch (error) {
            Logger.error('标记激活码为已使用失败:', error);
            return false;
        }
    }

    /**
     * 为用户分配激活码
     */
    async assignActivationCodeToUser(openid, codeType = 'vip_1d') {
        try {
            // 1. 检查用户是否已经获取过激活码
            const hasUsed = await this.hasUserUsedActivationCode(openid);
            if (hasUsed) {
                return { success: false, message: '您已经获取过激活码了哦~' };
            }

            // 2. 获取可用的激活码
            const activationCode = await this.getAvailableActivationCode(codeType);
            if (!activationCode) {
                return { success: false, message: '抱歉，激活码已发完，请联系管理员~' };
            }

            // 3. 标记激活码为已使用
            const marked = await this.markActivationCodeAsUsed(activationCode.id, openid);
            if (!marked) {
                return { success: false, message: '系统错误，请稍后重试~' };
            }

            // 4. 更新用户记录
            await this.botConnection.execute(`
                UPDATE wechat_users
                SET activation_code_used = ?, activation_code_time = NOW()
                WHERE openid = ?
            `, [activationCode.code, openid]);

            Logger.info(`为用户 ${openid} 分配激活码: ${activationCode.code}`);

            return {
                success: true,
                code: activationCode.code,
                message: '激活码获取成功！'
            };

        } catch (error) {
            Logger.error('分配激活码失败:', error);
            return { success: false, message: '系统错误，请稍后重试~' };
        }
    }

    // ==================== 关键词管理方法 ====================

    /**
     * 获取关键词配置
     */
    async getKeywordConfig(keyword) {
        try {
            const [rows] = await this.botConnection.execute(`
                SELECT * FROM reply_keywords
                WHERE keyword = ? AND is_active = 1
            `, [keyword]);

            return rows[0];
        } catch (error) {
            Logger.error('获取关键词配置失败:', error);
            return null;
        }
    }

    /**
     * 处理关键词回复
     */
    async handleKeywordReply(openid, keyword) {
        try {
            const config = await this.getKeywordConfig(keyword);
            if (!config) {
                return { success: false, message: '未识别的关键词' };
            }

            if (config.reply_type === 'activation_code') {
                // 激活码类型回复
                const result = await this.assignActivationCodeToUser(openid, config.activation_code_type);
                if (result.success) {
                    // 替换模板中的激活码
                    const replyText = config.reply_content.replace('{code}', result.code);
                    return { success: true, message: replyText };
                } else {
                    return result;
                }
            } else {
                // 普通文本回复
                return { success: true, message: config.reply_content };
            }
        } catch (error) {
            Logger.error('处理关键词回复失败:', error);
            return { success: false, message: '系统错误，请稍后重试~' };
        }
    }

    /**
     * 获取所有关键词配置
     */
    async getAllKeywords() {
        try {
            const [rows] = await this.botConnection.execute(`
                SELECT * FROM reply_keywords
                ORDER BY created_at DESC
            `);
            return rows;
        } catch (error) {
            Logger.error('获取关键词列表失败:', error);
            return [];
        }
    }

    /**
     * 添加关键词
     */
    async addKeyword(keyword, replyType, replyContent, activationCodeType = null) {
        try {
            const [result] = await this.botConnection.execute(`
                INSERT INTO reply_keywords (keyword, reply_type, reply_content, activation_code_type)
                VALUES (?, ?, ?, ?)
            `, [keyword, replyType, replyContent, activationCodeType]);

            return { success: true, id: result.insertId };
        } catch (error) {
            Logger.error('添加关键词失败:', error);
            return { success: false, message: '添加关键词失败' };
        }
    }

    async getUserActivationCode(keywordId, userId) {
        try {
            const [rows] = await this.activationConnection.execute(`
                SELECT * FROM activation_codes 
                WHERE used_by = ?
            `, [userId]);
            return rows[0];
        } catch (error) {
            Logger.error('获取用户激活码失败:', error);
            return null;
        }
    }

    async batchInsertActivationCodes(keywordId, codes) {
        if (codes.length === 0) return [];
        
        try {
            const placeholders = codes.map(() => '(?)').join(', ');
            const [result] = await this.activationConnection.execute(
                `INSERT INTO activation_codes (code) VALUES ${placeholders}`,
                codes
            );
            
            const results = [];
            for (let i = 0; i < codes.length; i++) {
                results.push({
                    id: result.insertId + i,
                    code: codes[i]
                });
            }
            return results;
        } catch (error) {
            Logger.error('批量插入激活码失败:', error);
            throw error;
        }
    }

    async getActivationCodesByKeyword(keywordId) {
        try {
            // 返回所有激活码（因为激活码表中没有keyword_id字段）
            const [rows] = await this.activationConnection.execute(
                'SELECT * FROM activation_codes ORDER BY id DESC'
            );
            return rows;
        } catch (error) {
            Logger.error('获取激活码失败:', error);
            return [];
        }
    }

    async getAllActivationCodes() {
        try {
            const [rows] = await this.activationConnection.execute(
                'SELECT * FROM activation_codes ORDER BY id DESC'
            );
            return rows;
        } catch (error) {
            Logger.error('获取所有激活码失败:', error);
            return [];
        }
    }

    async getActivationCodeByCode(code) {
        try {
            const [rows] = await this.activationConnection.execute('SELECT * FROM activation_codes WHERE code = ?', [code]);
            return rows[0];
        } catch (error) {
            Logger.error('根据代码获取激活码失败:', error);
            return null;
        }
    }

    async addActivationCode(keywordId, code) {
        try {
            const [result] = await this.activationConnection.execute(
                'INSERT INTO activation_codes (code) VALUES (?)', 
                [code]
            );
            return { id: result.insertId, code };
        } catch (error) {
            Logger.error('添加激活码失败:', error);
            throw error;
        }
    }

    async removeActivationCode(id) {
        try {
            const [result] = await this.activationConnection.execute('DELETE FROM activation_codes WHERE id = ?', [id]);
            return result;
        } catch (error) {
            Logger.error('删除激活码失败:', error);
            throw error;
        }
    }

    async getActivationCodeStats(keywordId) {
        try {
            const [rows] = await this.activationConnection.execute(`
                SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN used_by IS NOT NULL AND used_by != '' THEN 1 END) as used
                FROM activation_codes
            `);
            return rows[0];
        } catch (error) {
            Logger.error('获取激活码统计失败:', error);
            return { total: 0, used: 0 };
        }
    }

    async resetActivationCodes(keywordId) {
        try {
            const [result] = await this.activationConnection.execute(
                'UPDATE activation_codes SET used_by = NULL, used_at = NULL'
            );
            return result.affectedRows;
        } catch (error) {
            Logger.error('重置激活码失败:', error);
            throw error;
        }
    }

    /**
     * 关闭数据库连接
     */
    async close() {
        if (this.activationConnection) {
            await this.activationConnection.end();
            Logger.info('激活码数据库连接已关闭');
        }
        if (this.keywordConnection && this.keywordConnection !== this.activationConnection) {
            await this.keywordConnection.end();
            Logger.info('关键词数据库连接已关闭');
        }
    }

    /**
     * 测试数据库连接
     */
    async testConnection() {
        try {
            await this.activationConnection.ping();
            if (this.keywordConnection !== this.activationConnection) {
                await this.keywordConnection.ping();
            }
            return true;
        } catch (error) {
            Logger.error('数据库连接测试失败:', error);
            return false;
        }
    }
}

module.exports = DatabaseService;
