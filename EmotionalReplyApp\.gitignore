# 情感回复助手项目 .gitignore

# ===== 通用忽略 =====
*.log
*.tmp
*.temp
.DS_Store
Thumbs.db
.env
.env.local
.env.production

# ===== 后端 (Java/Spring Boot) =====
backend-service/target/
backend-service/bin/
backend-service/.mvn/
backend-service/mvnw
backend-service/mvnw.cmd
backend-service/*.iml
backend-service/.idea/
backend-service/.vscode/
backend-service/.settings/
backend-service/.project
backend-service/.classpath
backend-service/logs/

# ===== 前端 (uni-app) =====
uniapp-frontend/node_modules/
uniapp-frontend/dist/
uniapp-frontend/unpackage/
uniapp-frontend/.hbuilderx/
uniapp-frontend/.vscode/
uniapp-frontend/package-lock.json
uniapp-frontend/yarn.lock

# ===== 部署相关 =====
deploy/.env
deploy/logs/
deploy/ssl/
deploy/config/application-prod.yml

# ===== 构建产物 =====
*.jar
*.war
*.apk
*.ipa
build-info.json

# ===== 开发工具 =====
.idea/
.vscode/
*.swp
*.swo
*~

# ===== 系统文件 =====
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ===== 备份文件 =====
*.bak
*.backup
*.old

# ===== 临时文件 =====
tmp/
temp/
cache/
