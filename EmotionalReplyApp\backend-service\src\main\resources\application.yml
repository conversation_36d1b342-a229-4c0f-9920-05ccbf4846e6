# ========================================
# 情感回复助手后端服务配置
# 适用于宝塔面板部署
# ========================================

server:
  port: ${SERVER_PORT:8081}
  address: 0.0.0.0
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  # 生产环境优化
  tomcat:
    max-threads: 200
    min-spare-threads: 10
    connection-timeout: 20000
    accept-count: 100

# ========================================
# Spring 核心配置
# ========================================
spring:
  application:
    name: emotional-reply-service
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:prod}

  # ========================================
  # 数据库配置 (MySQL)
  # ========================================
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:emotional_reply_db}?useUnicode=true&characterEncoding=utf8&useSSL=${DB_SSL:false}&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true&rewriteBatchedStatements=true
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:b683ffdbf5ffb6b9}

    # HikariCP连接池配置 (生产环境优化)
    hikari:
      minimum-idle: ${DB_MIN_IDLE:10}
      maximum-pool-size: ${DB_MAX_POOL:30}
      auto-commit: true
      idle-timeout: 600000              # 10分钟
      pool-name: EmotionalReplyHikariCP
      max-lifetime: 1800000             # 30分钟
      connection-timeout: 30000         # 30秒
      connection-test-query: SELECT 1
      leak-detection-threshold: 60000   # 连接泄漏检测

  # ========================================
  # Redis配置 (缓存和会话存储)
  # ========================================
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:123qqwzry.}
    database: ${REDIS_DATABASE:0}
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

  # ========================================
  # JSON序列化配置
  # ========================================
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: NON_NULL
    serialization:
      write-dates-as-timestamps: false
      indent-output: false
    deserialization:
      fail-on-unknown-properties: false

  # ========================================
  # 文件上传配置
  # ========================================
  servlet:
    multipart:
      max-file-size: ${MAX_FILE_SIZE:10MB}
      max-request-size: ${MAX_REQUEST_SIZE:50MB}
      enabled: true

  # ========================================
  # 邮件服务配置
  # ========================================
  mail:
    host: ${MAIL_HOST:smtp.qq.com}
    port: ${MAIL_PORT:465}
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:qogcxxuxixebfbbg}
    default-encoding: UTF-8
    properties:
      mail:
        smtp:
          auth: true
          ssl:
            enable: true
          socketFactory:
            port: 465
            class: javax.net.ssl.SSLSocketFactory
          connectiontimeout: 10000
          timeout: 10000
          writetimeout: 10000

# ========================================
# MyBatis Plus 配置
# ========================================
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: null
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
    banner: false
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.emotional.service.entity

# ========================================
# 日志配置 (生产环境优化)
# ========================================
logging:
  level:
    root: ${LOG_LEVEL_ROOT:INFO}
    com.emotional.service: ${LOG_LEVEL_APP:INFO}
    com.baomidou.mybatisplus: WARN
    org.springframework.web: WARN
    org.springframework.security: WARN
    org.springframework.boot.autoconfigure: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: ${LOG_FILE_PATH:/www/wwwroot/logs}/emotional-reply-service.log
    max-size: ${LOG_MAX_SIZE:50MB}
    max-history: ${LOG_MAX_HISTORY:15}
    total-size-cap: ${LOG_TOTAL_SIZE:1GB}
  logback:
    rollingpolicy:
      clean-history-on-start: true

# ========================================
# 监控和健康检查配置
# ========================================
management:
  endpoints:
    web:
      exposure:
        include: ${ACTUATOR_ENDPOINTS:health,info,metrics}
      base-path: /actuator
  endpoint:
    health:
      show-details: ${HEALTH_SHOW_DETAILS:when-authorized}
      show-components: always
  health:
    db:
      enabled: ${HEALTH_DB_ENABLED:true}
    mail:
      enabled: ${HEALTH_MAIL_ENABLED:false}
    redis:
      enabled: ${HEALTH_REDIS_ENABLED:true}
  info:
    env:
      enabled: true
    java:
      enabled: true
    os:
      enabled: true

# ========================================
# API文档配置 (Swagger)
# ========================================
springdoc:
  api-docs:
    path: /api-docs
    enabled: ${SWAGGER_ENABLED:true}
  swagger-ui:
    path: /swagger-ui.html
    enabled: ${SWAGGER_UI_ENABLED:true}
    operations-sorter: method
    tags-sorter: alpha
  show-actuator: false

# ========================================
# 应用自定义配置
# ========================================
app:
  # ========================================
  # LLM服务配置
  # ========================================
  llm:
    deepseek:
      enabled: ${DEEPSEEK_ENABLED:true}
      api-key: ${DEEPSEEK_API_KEY:***********************************}
      base-url: ${DEEPSEEK_BASE_URL:https://api.deepseek.com/v1}
      model: ${DEEPSEEK_MODEL:deepseek-chat}
      timeout: ${DEEPSEEK_TIMEOUT:30000}
      balance-warning-threshold: ${DEEPSEEK_BALANCE_WARNING:5.0}
      balance-check-enabled: ${DEEPSEEK_BALANCE_CHECK:true}
      max-retries: ${DEEPSEEK_MAX_RETRIES:3}
      retry-delay: ${DEEPSEEK_RETRY_DELAY:1000}

  # ========================================
  # 用户配置
  # ========================================
  user:
    max-daily-requests: ${USER_MAX_DAILY_REQUESTS:100}
    vip-max-daily-requests: ${VIP_MAX_DAILY_REQUESTS:1000}
    session-timeout: ${USER_SESSION_TIMEOUT:86400}

  # ========================================
  # 安全配置
  # ========================================
  security:
    jwt-secret: ${JWT_SECRET:emotional-reply-service-super-secure-jwt-secret-key-2025}
    jwt-expire: ${JWT_EXPIRE:86400}
    password-salt: ${PASSWORD_SALT:emotional-reply-salt-2025}

  # ========================================
  # 文件存储配置
  # ========================================
  file:
    upload-path: ${FILE_UPLOAD_PATH:/www/wwwroot/uploads}
    max-size: ${FILE_MAX_SIZE:10485760}
    allowed-types: ${FILE_ALLOWED_TYPES:jpg,jpeg,png,gif,pdf,doc,docx}

  # ========================================
  # 管理员配置
  # ========================================
  admin:
    email: ${ADMIN_EMAIL:<EMAIL>}
    notification:
      enabled: ${ADMIN_NOTIFICATION_ENABLED:true}
      throttle:
        enabled: ${ADMIN_NOTIFICATION_THROTTLE_ENABLED:true}
        api-key-error: ${ADMIN_NOTIFICATION_THROTTLE_API_KEY:86400}
        balance-error: ${ADMIN_NOTIFICATION_THROTTLE_BALANCE:3600}
        service-error: ${ADMIN_NOTIFICATION_THROTTLE_SERVICE:1800}

# ========================================
# 环境特定配置
# ========================================

---
# 开发环境配置
spring:
  profiles: dev
  datasource:
    hikari:
      maximum-pool-size: 10
      minimum-idle: 2

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

logging:
  level:
    root: INFO
    com.emotional.service: DEBUG
  file:
    name: logs/emotional-reply-dev.log

---
# 生产环境配置 (宝塔部署)
spring:
  profiles: prod
  datasource:
    hikari:
      maximum-pool-size: 30
      minimum-idle: 10
      leak-detection-threshold: 60000

logging:
  level:
    root: WARN
    com.emotional.service: INFO
  file:
    name: /www/wwwroot/logs/emotional-reply-service.log

management:
  endpoints:
    web:
      exposure:
        include: health,info

springdoc:
  swagger-ui:
    enabled: false  # 生产环境关闭Swagger UI
