package com.emotional.service.dto;

import com.emotional.service.entity.ActivationCode;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 激活码DTO，包含使用者信息
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
public class ActivationCodeDTO {
    
    /**
     * 激活码ID
     */
    private Long id;
    
    /**
     * 激活码
     */
    private String code;
    
    /**
     * 激活码类型
     */
    private String codeType;
    
    /**
     * 有效天数
     */
    private Integer durationDays;
    
    /**
     * 批次ID
     */
    private String batchId;
    
    /**
     * 创建者用户ID
     */
    private Long createdBy;
    
    /**
     * 使用者用户ID
     */
    private Long usedBy;
    
    /**
     * 使用者用户名
     */
    private String usedByUsername;
    
    /**
     * 使用者昵称
     */
    private String usedByNickname;
    
    /**
     * 使用时间
     */
    private LocalDateTime usedTime;
    
    /**
     * 状态：0-未使用，1-已使用
     */
    private Integer status;
    
    /**
     * 备注信息
     */
    private String remark;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
    
    /**
     * 从ActivationCode实体转换为DTO
     */
    public static ActivationCodeDTO fromEntity(ActivationCode activationCode) {
        ActivationCodeDTO dto = new ActivationCodeDTO();
        dto.setId(activationCode.getId());
        dto.setCode(activationCode.getCode());
        dto.setCodeType(activationCode.getCodeType());
        dto.setDurationDays(activationCode.getDurationDays());
        dto.setBatchId(activationCode.getBatchId());
        dto.setCreatedBy(activationCode.getCreatedBy());
        dto.setUsedBy(activationCode.getUsedBy());
        dto.setUsedTime(activationCode.getUsedTime());
        dto.setStatus(activationCode.getStatus());
        dto.setRemark(activationCode.getRemark());
        dto.setCreatedTime(activationCode.getCreatedTime());
        dto.setUpdatedTime(activationCode.getUpdatedTime());
        return dto;
    }
    
    /**
     * 设置使用者信息
     */
    public void setUserInfo(String username, String nickname) {
        this.usedByUsername = username;
        this.usedByNickname = nickname;
    }
}
