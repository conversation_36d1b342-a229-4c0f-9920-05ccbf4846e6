const DatabaseService = require('../src/services/databaseService');
const KeywordMatcher = require('../src/services/keywordMatcher');
const ActivationCodeManager = require('../src/services/activationCodeManager');
const WeChatService = require('../src/services/wechatService');

describe('微信激活码机器人测试', () => {
    let db, keywordMatcher, activationCodeManager, wechatService;

    beforeAll(async () => {
        // 使用内存数据库进行测试
        process.env.DB_PATH = ':memory:';
        db = new DatabaseService();
        await db.initialize();
        
        keywordMatcher = new KeywordMatcher(db);
        activationCodeManager = new ActivationCodeManager(db);
        wechatService = new WeChatService('test_token');
    });

    afterAll(async () => {
        if (db) {
            db.close();
        }
    });

    describe('数据库服务测试', () => {
        test('应该能够添加关键字', async () => {
            const keyword = await db.addKeyword('测试关键字', '测试描述');
            expect(keyword).toBeDefined();
            expect(keyword.keyword).toBe('测试关键字');
            expect(keyword.description).toBe('测试描述');
        });

        test('应该能够获取所有关键字', async () => {
            const keywords = await db.getAllKeywords();
            expect(Array.isArray(keywords)).toBe(true);
            expect(keywords.length).toBeGreaterThan(0);
        });
    });

    describe('关键字匹配测试', () => {
        let testKeywordId;

        beforeAll(async () => {
            const keyword = await db.addKeyword('VIP会员', 'VIP会员激活码');
            testKeywordId = keyword.id;
        });

        test('应该能够精确匹配关键字', async () => {
            const match = await keywordMatcher.findMatch('VIP会员');
            expect(match).toBeDefined();
            expect(match.keyword).toBe('VIP会员');
        });

        test('应该能够模糊匹配关键字', async () => {
            const match = await keywordMatcher.findMatch('vip会员');
            expect(match).toBeDefined();
            expect(match.keyword).toBe('VIP会员');
        });

        test('不匹配的关键字应该返回null', async () => {
            const match = await keywordMatcher.findMatch('不存在的关键字');
            expect(match).toBeNull();
        });
    });

    describe('激活码管理测试', () => {
        let testKeywordId;

        beforeAll(async () => {
            const keyword = await db.addKeyword('测试激活码', '测试激活码描述');
            testKeywordId = keyword.id;
        });

        test('应该能够生成激活码', async () => {
            const codes = await activationCodeManager.generateActivationCodes(testKeywordId, 5);
            expect(codes).toBeDefined();
            expect(codes.length).toBe(5);
            codes.forEach(code => {
                expect(code.code).toMatch(/^[A-Z0-9]+$/);
            });
        });

        test('应该能够获取可用的激活码', async () => {
            const code = await activationCodeManager.getActivationCode(testKeywordId, 'test_user_1');
            expect(code).toBeDefined();
            expect(code.code).toMatch(/^[A-Z0-9]+$/);
        });

        test('同一用户不应该重复获取激活码', async () => {
            const code1 = await activationCodeManager.getActivationCode(testKeywordId, 'test_user_2');
            const code2 = await activationCodeManager.getActivationCode(testKeywordId, 'test_user_2');
            
            expect(code1).toBeDefined();
            expect(code2).toBeDefined();
            expect(code1.code).toBe(code2.code);
        });

        test('应该能够获取激活码统计信息', async () => {
            const stats = await activationCodeManager.getActivationCodeStats(testKeywordId);
            expect(stats).toBeDefined();
            expect(typeof stats.total).toBe('number');
            expect(typeof stats.used).toBe('number');
            expect(typeof stats.available).toBe('number');
        });
    });

    describe('微信服务测试', () => {
        test('应该能够验证签名', () => {
            const timestamp = '1234567890';
            const nonce = 'test_nonce';
            const signature = wechatService.verifySignature('invalid_signature', timestamp, nonce);
            expect(typeof signature).toBe('boolean');
        });

        test('应该能够创建文本回复', () => {
            const response = wechatService.createTextResponse('toUser', 'fromUser', '测试消息');
            expect(response).toContain('<xml>');
            expect(response).toContain('测试消息');
            expect(response).toContain('toUser');
            expect(response).toContain('fromUser');
        });

        test('应该能够解析XML消息', async () => {
            const xmlData = `
                <xml>
                    <ToUserName><![CDATA[toUser]]></ToUserName>
                    <FromUserName><![CDATA[fromUser]]></FromUserName>
                    <CreateTime>1234567890</CreateTime>
                    <MsgType><![CDATA[text]]></MsgType>
                    <Content><![CDATA[测试内容]]></Content>
                </xml>
            `;
            
            const message = await wechatService.parseMessage(xmlData);
            expect(message).toBeDefined();
            expect(message.ToUserName).toBe('toUser');
            expect(message.FromUserName).toBe('fromUser');
            expect(message.MsgType).toBe('text');
            expect(message.Content).toBe('测试内容');
        });
    });

    describe('集成测试', () => {
        let testKeywordId;

        beforeAll(async () => {
            const keyword = await db.addKeyword('集成测试', '集成测试激活码');
            testKeywordId = keyword.id;
            await activationCodeManager.generateActivationCodes(testKeywordId, 10);
        });

        test('完整的消息处理流程', async () => {
            // 模拟微信消息
            const mockMessage = {
                ToUserName: 'bot',
                FromUserName: 'test_user',
                MsgType: 'text',
                Content: '集成测试',
                CreateTime: '1234567890'
            };

            // 查找关键字
            const matchedKeyword = await keywordMatcher.findMatch(mockMessage.Content);
            expect(matchedKeyword).toBeDefined();

            // 获取激活码
            const activationCode = await activationCodeManager.getActivationCode(
                matchedKeyword.id,
                mockMessage.FromUserName
            );
            expect(activationCode).toBeDefined();

            // 创建回复
            const response = wechatService.createTextResponse(
                mockMessage.FromUserName,
                mockMessage.ToUserName,
                `您的激活码是：${activationCode.code}`
            );
            expect(response).toContain(activationCode.code);
        });
    });
});
