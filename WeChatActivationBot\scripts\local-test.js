#!/usr/bin/env node

/**
 * 本地测试脚本 - 模拟微信消息处理流程
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const DatabaseService = require('../src/services/databaseService');
const KeywordMatcher = require('../src/services/keywordMatcher');
const ActivationCodeManager = require('../src/services/activationCodeManager');
const WeChatService = require('../src/services/wechatService');
const Logger = require('../src/utils/logger');

class LocalTester {
    constructor() {
        this.db = null;
        this.keywordMatcher = null;
        this.activationCodeManager = null;
        this.wechatService = null;
    }

    async initialize() {
        try {
            Logger.info('初始化本地测试环境...');
            
            // 初始化服务
            this.db = new DatabaseService();
            await this.db.initialize();
            
            this.keywordMatcher = new KeywordMatcher(this.db);
            this.activationCodeManager = new ActivationCodeManager(this.db);
            this.wechatService = new WeChatService(process.env.WECHAT_TOKEN || 'test_token');
            
            Logger.info('本地测试环境初始化完成');
        } catch (error) {
            Logger.error('初始化失败:', error);
            throw error;
        }
    }

    async testKeywordMatching() {
        console.log('\n=== 测试关键字匹配 ===');
        
        const testKeywords = ['VIP会员', '高级版', '专业版', '测试'];
        
        for (const keyword of testKeywords) {
            const match = await this.keywordMatcher.findMatch(keyword);
            if (match) {
                console.log(`✅ 关键字 "${keyword}" 匹配成功: ${match.keyword} (ID: ${match.id})`);
            } else {
                console.log(`❌ 关键字 "${keyword}" 未找到匹配`);
            }
        }
    }

    async testActivationCodeGeneration() {
        console.log('\n=== 测试激活码生成 ===');
        
        const keywords = await this.db.getAllKeywords();
        
        for (const keyword of keywords.slice(0, 2)) { // 只测试前两个关键字
            const stats = await this.activationCodeManager.getActivationCodeStats(keyword.id);
            console.log(`关键字 "${keyword.keyword}": 总计 ${stats.total} 个，已用 ${stats.used} 个，可用 ${stats.available} 个`);
            
            if (stats.available < 5) {
                console.log(`为关键字 "${keyword.keyword}" 生成更多激活码...`);
                await this.activationCodeManager.generateActivationCodes(keyword.id, 10);
                console.log(`✅ 已生成 10 个新激活码`);
            }
        }
    }

    async simulateUserInteraction(userId, message) {
        console.log(`\n=== 模拟用户交互 ===`);
        console.log(`用户 ${userId} 发送消息: "${message}"`);
        
        try {
            // 1. 匹配关键字
            const matchedKeyword = await this.keywordMatcher.findMatch(message);
            
            if (!matchedKeyword) {
                console.log(`❌ 未找到匹配的关键字`);
                return '未找到匹配的关键字。请检查您的输入或联系管理员。';
            }
            
            console.log(`✅ 匹配到关键字: ${matchedKeyword.keyword}`);
            
            // 2. 获取激活码
            const activationCode = await this.activationCodeManager.getActivationCode(
                matchedKeyword.id,
                userId
            );
            
            if (!activationCode) {
                console.log(`❌ 该关键字的激活码已用完`);
                return '抱歉，该关键字的激活码已用完。请联系管理员。';
            }
            
            console.log(`✅ 分配激活码: ${activationCode.code}`);
            
            // 3. 生成回复消息
            const responseText = `您的激活码是：${activationCode.code}\n\n请妥善保管，激活码仅可使用一次。`;
            
            // 4. 创建微信格式的回复
            const wechatResponse = this.wechatService.createTextResponse(
                userId,
                'bot',
                responseText
            );
            
            console.log(`✅ 回复内容: ${responseText}`);
            return responseText;
            
        } catch (error) {
            Logger.error('模拟用户交互失败:', error);
            return '系统繁忙，请稍后再试。';
        }
    }

    async testMultipleUsers() {
        console.log('\n=== 测试多用户场景 ===');
        
        const users = ['user001', 'user002', 'user003'];
        const keyword = 'VIP会员';
        
        for (const userId of users) {
            await this.simulateUserInteraction(userId, keyword);
            console.log('---');
        }
        
        // 测试重复请求
        console.log('测试用户重复请求:');
        await this.simulateUserInteraction('user001', keyword);
    }

    async showStatistics() {
        console.log('\n=== 系统统计信息 ===');
        
        const keywords = await this.db.getAllKeywords();
        console.log(`总关键字数: ${keywords.length}`);
        
        for (const keyword of keywords) {
            const stats = await this.activationCodeManager.getActivationCodeStats(keyword.id);
            console.log(`${keyword.keyword}: 总计 ${stats.total}, 已用 ${stats.used}, 可用 ${stats.available}`);
        }
    }

    async cleanup() {
        if (this.db) {
            this.db.close();
        }
    }
}

// 主测试函数
async function runTests() {
    const tester = new LocalTester();
    
    try {
        await tester.initialize();
        
        // 运行各种测试
        await tester.testKeywordMatching();
        await tester.testActivationCodeGeneration();
        await tester.simulateUserInteraction('test_user_001', 'VIP会员');
        await tester.simulateUserInteraction('test_user_002', 'vip会员'); // 测试模糊匹配
        await tester.simulateUserInteraction('test_user_003', '不存在的关键字');
        await tester.testMultipleUsers();
        await tester.showStatistics();
        
        console.log('\n✅ 所有测试完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
    } finally {
        await tester.cleanup();
    }
}

// 交互式测试函数
async function interactiveTest() {
    const readline = require('readline');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });
    
    const tester = new LocalTester();
    await tester.initialize();
    
    console.log('\n🤖 微信激活码机器人 - 交互式测试');
    console.log('输入关键字获取激活码，输入 "quit" 退出\n');
    
    const askQuestion = () => {
        rl.question('请输入关键字: ', async (input) => {
            if (input.toLowerCase() === 'quit') {
                console.log('再见！');
                rl.close();
                await tester.cleanup();
                return;
            }
            
            const userId = `interactive_user_${Date.now()}`;
            const response = await tester.simulateUserInteraction(userId, input);
            console.log(`\n🤖 机器人回复: ${response}\n`);
            
            askQuestion();
        });
    };
    
    askQuestion();
}

// 命令行参数处理
const args = process.argv.slice(2);
const command = args[0];

if (command === 'interactive') {
    interactiveTest();
} else {
    runTests();
}

module.exports = LocalTester;
