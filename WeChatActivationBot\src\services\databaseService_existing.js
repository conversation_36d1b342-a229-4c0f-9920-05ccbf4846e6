const mysql = require('mysql2/promise');
const Logger = require('../utils/logger');

class DatabaseService {
    constructor() {
        this.connection = null;
        this.config = {
            host: process.env.DB_HOST || 'localhost',
            port: process.env.DB_PORT || 3306,
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || '',
            database: process.env.DB_NAME || 'emotional_reply_db',
            charset: 'utf8mb4'
        };
    }

    /**
     * 初始化数据库连接
     */
    async initialize() {
        try {
            // 连接到现有数据库
            this.connection = await mysql.createConnection(this.config);
            
            // 检查并创建缺失的表或字段
            await this.ensureTablesExist();
            
            Logger.info(`已连接到现有数据库: ${this.config.database}`);
        } catch (error) {
            Logger.error('数据库连接失败:', error);
            throw error;
        }
    }

    /**
     * 确保必要的表和字段存在
     */
    async ensureTablesExist() {
        try {
            // 检查activation_codes表是否存在
            const [tables] = await this.connection.execute(`
                SELECT TABLE_NAME 
                FROM information_schema.TABLES 
                WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'activation_codes'
            `, [this.config.database]);

            if (tables.length === 0) {
                // 如果表不存在，创建它
                await this.connection.execute(`
                    CREATE TABLE activation_codes (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        keyword_id INT,
                        code VARCHAR(255) UNIQUE NOT NULL,
                        used_by VARCHAR(255) NULL,
                        used_at TIMESTAMP NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        INDEX idx_code (code),
                        INDEX idx_used_by (used_by)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                `);
                Logger.info('创建了activation_codes表');
            }

            // 检查是否需要创建keywords表（如果不存在）
            const [keywordTables] = await this.connection.execute(`
                SELECT TABLE_NAME 
                FROM information_schema.TABLES 
                WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'keywords'
            `, [this.config.database]);

            if (keywordTables.length === 0) {
                await this.connection.execute(`
                    CREATE TABLE keywords (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        keyword VARCHAR(255) UNIQUE NOT NULL,
                        description TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        INDEX idx_keyword (keyword)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                `);
                Logger.info('创建了keywords表');
            }

        } catch (error) {
            Logger.error('检查表结构失败:', error);
            // 不抛出错误，继续使用现有结构
        }
    }

    // ==================== 关键字相关方法 ====================
    // 由于没有keywords表，我们基于activation_codes表中的数据来模拟关键字功能

    async getAllKeywords() {
        try {
            // 从activation_codes表中获取所有不同的关键字信息
            // 假设activation_codes表有keyword_name或类似字段，或者我们使用固定的关键字

            // 先检查表结构
            const [columns] = await this.connection.execute(`
                SELECT COLUMN_NAME
                FROM information_schema.COLUMNS
                WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'activation_codes'
            `, [this.config.database]);

            const columnNames = columns.map(col => col.COLUMN_NAME);
            Logger.info('activation_codes表字段:', columnNames);

            // 返回一个默认的关键字列表，基于现有激活码
            const [codeCount] = await this.connection.execute('SELECT COUNT(*) as total FROM activation_codes');

            return [
                {
                    id: 1,
                    keyword: 'emo',
                    description: `情感助手激活码 (共${codeCount[0].total}个激活码)`,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                }
            ];
        } catch (error) {
            Logger.error('获取关键字列表失败:', error);
            return [];
        }
    }

    async getKeywordById(id) {
        // 返回默认关键字
        if (id === 1) {
            const [codeCount] = await this.connection.execute('SELECT COUNT(*) as total FROM activation_codes');
            return {
                id: 1,
                keyword: 'emo',
                description: `情感助手激活码 (共${codeCount[0].total}个激活码)`,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };
        }
        return null;
    }

    async addKeyword(keyword, description = '') {
        // 由于没有keywords表，我们不能真正添加关键字
        // 但可以返回一个虚拟的关键字对象
        Logger.warn('当前数据库没有keywords表，无法添加新关键字');
        throw new Error('当前数据库结构不支持添加关键字，请直接管理激活码');
    }

    async removeKeyword(id) {
        Logger.warn('当前数据库没有keywords表，无法删除关键字');
        throw new Error('当前数据库结构不支持删除关键字');
    }

    // ==================== 激活码相关方法 ====================

    async getAvailableActivationCode(keywordId) {
        try {
            // 由于没有keyword_id字段关联，我们直接获取任何未使用的激活码
            const [rows] = await this.connection.execute(`
                SELECT * FROM activation_codes
                WHERE used_by IS NULL OR used_by = ''
                ORDER BY id ASC
                LIMIT 1
            `);
            return rows[0];
        } catch (error) {
            Logger.error('获取可用激活码失败:', error);
            return null;
        }
    }

    async assignActivationCode(codeId, userId) {
        try {
            const [result] = await this.connection.execute(
                'UPDATE activation_codes SET used_by = ?, used_at = NOW() WHERE id = ?', 
                [userId, codeId]
            );
            return result;
        } catch (error) {
            Logger.error('分配激活码失败:', error);
            throw error;
        }
    }

    async getUserActivationCode(keywordId, userId) {
        try {
            const [rows] = await this.connection.execute(`
                SELECT * FROM activation_codes
                WHERE used_by = ?
            `, [userId]);
            return rows[0];
        } catch (error) {
            Logger.error('获取用户激活码失败:', error);
            return null;
        }
    }

    async batchInsertActivationCodes(keywordId, codes) {
        if (codes.length === 0) return [];

        try {
            // 检查表结构，看看有哪些字段
            const [columns] = await this.connection.execute(`
                SELECT COLUMN_NAME
                FROM information_schema.COLUMNS
                WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'activation_codes'
            `, [this.config.database]);

            const columnNames = columns.map(col => col.COLUMN_NAME);

            // 根据实际表结构插入数据
            if (columnNames.includes('code')) {
                const placeholders = codes.map(() => '(?)').join(', ');
                const [result] = await this.connection.execute(
                    `INSERT INTO activation_codes (code) VALUES ${placeholders}`,
                    codes
                );

                const results = [];
                for (let i = 0; i < codes.length; i++) {
                    results.push({
                        id: result.insertId + i,
                        code: codes[i]
                    });
                }
                return results;
            } else {
                throw new Error('activation_codes表中没有找到code字段');
            }
        } catch (error) {
            Logger.error('批量插入激活码失败:', error);
            throw error;
        }
    }

    async getActivationCodesByKeyword(keywordId) {
        try {
            // 由于没有keyword_id字段，返回所有激活码
            const [rows] = await this.connection.execute(
                'SELECT * FROM activation_codes ORDER BY id DESC'
            );
            return rows;
        } catch (error) {
            Logger.error('获取激活码失败:', error);
            return [];
        }
    }

    async getAllActivationCodes() {
        try {
            const [rows] = await this.connection.execute(
                'SELECT * FROM activation_codes ORDER BY created_at DESC'
            );
            return rows;
        } catch (error) {
            Logger.error('获取所有激活码失败:', error);
            return [];
        }
    }

    async getActivationCodeByCode(code) {
        try {
            const [rows] = await this.connection.execute('SELECT * FROM activation_codes WHERE code = ?', [code]);
            return rows[0];
        } catch (error) {
            Logger.error('根据代码获取激活码失败:', error);
            return null;
        }
    }

    async addActivationCode(keywordId, code) {
        try {
            const [result] = await this.connection.execute(
                'INSERT INTO activation_codes (code) VALUES (?)',
                [code]
            );
            return { id: result.insertId, code };
        } catch (error) {
            Logger.error('添加激活码失败:', error);
            throw error;
        }
    }

    async removeActivationCode(id) {
        try {
            const [result] = await this.connection.execute('DELETE FROM activation_codes WHERE id = ?', [id]);
            return result;
        } catch (error) {
            Logger.error('删除激活码失败:', error);
            throw error;
        }
    }

    async getActivationCodeStats(keywordId) {
        try {
            const [rows] = await this.connection.execute(`
                SELECT
                    COUNT(*) as total,
                    COUNT(CASE WHEN used_by IS NOT NULL AND used_by != '' THEN 1 END) as used
                FROM activation_codes
            `);
            return rows[0];
        } catch (error) {
            Logger.error('获取激活码统计失败:', error);
            return { total: 0, used: 0 };
        }
    }

    async resetActivationCodes(keywordId) {
        try {
            const [result] = await this.connection.execute(
                'UPDATE activation_codes SET used_by = NULL, used_at = NULL'
            );
            return result.affectedRows;
        } catch (error) {
            Logger.error('重置激活码失败:', error);
            throw error;
        }
    }

    /**
     * 关闭数据库连接
     */
    async close() {
        if (this.connection) {
            await this.connection.end();
            Logger.info('数据库连接已关闭');
        }
    }

    /**
     * 测试数据库连接
     */
    async testConnection() {
        try {
            await this.connection.ping();
            return true;
        } catch (error) {
            Logger.error('数据库连接测试失败:', error);
            return false;
        }
    }
}

module.exports = DatabaseService;
