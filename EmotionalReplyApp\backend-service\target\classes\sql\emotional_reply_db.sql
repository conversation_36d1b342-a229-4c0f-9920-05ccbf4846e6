/*
 Navicat Premium Dump SQL

 Source Server         : YUMU
 Source Server Type    : MySQL
 Source Server Version : 80039 (8.0.39)
 Source Host           : localhost:3306
 Source Schema         : emotional_reply_db

 Target Server Type    : MySQL
 Target Server Version : 80039 (8.0.39)
 File Encoding         : 65001

 Date: 02/07/2025 08:59:39
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for activation_codes
-- ----------------------------
DROP TABLE IF EXISTS `activation_codes`;
CREATE TABLE `activation_codes`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '激活码ID',
  `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '激活码',
  `code_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '激活码类型：vip_1d-1天VIP，vip_7d-7天VIP，vip_1m-1个月VIP，vip_3m-3个月VIP，vip_6m-6个月VIP，vip_1y-1年VIP',
  `duration_days` int NOT NULL COMMENT '有效天数',
  `batch_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '批次ID',
  `created_by` bigint NOT NULL COMMENT '创建者(管理员ID)',
  `used_by` bigint NULL DEFAULT NULL COMMENT '使用者用户ID',
  `used_time` datetime NULL DEFAULT NULL COMMENT '使用时间',
  `status` tinyint NULL DEFAULT 0 COMMENT '状态：0-未使用，1-已使用',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注信息',
  `created_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `code`(`code` ASC) USING BTREE,
  UNIQUE INDEX `uk_code`(`code` ASC) USING BTREE,
  INDEX `idx_batch_id`(`batch_id` ASC) USING BTREE,
  INDEX `idx_created_by`(`created_by` ASC) USING BTREE,
  INDEX `idx_used_by`(`used_by` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_code_type`(`code_type` ASC) USING BTREE,
  CONSTRAINT `activation_codes_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `activation_codes_ibfk_2` FOREIGN KEY (`used_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '激活码表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_versions
-- ----------------------------
DROP TABLE IF EXISTS `app_versions`;
CREATE TABLE `app_versions`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '版本ID',
  `version_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '版本名称，如1.0.0',
  `version_code` int NOT NULL COMMENT '版本号，用于比较大小',
  `platform` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'android' COMMENT '平台类型：android, ios, h5',
  `update_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '更新内容描述',
  `download_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '下载链接',
  `file_size` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文件大小，如10.5MB',
  `is_force_update` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否强制更新：0-否，1-是',
  `min_support_version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最低支持版本',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'draft' COMMENT '状态：draft-草稿，published-已发布，deleted-已删除',
  `release_date` datetime NULL DEFAULT NULL COMMENT '发布时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_version_platform`(`version_name` ASC, `platform` ASC) USING BTREE,
  INDEX `idx_platform_status`(`platform` ASC, `status` ASC) USING BTREE,
  INDEX `idx_version_code`(`version_code` ASC) USING BTREE,
  INDEX `idx_release_date`(`release_date` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '应用版本管理表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for reply_history
-- ----------------------------
DROP TABLE IF EXISTS `reply_history`;
CREATE TABLE `reply_history`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '历史记录ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `original_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '原始消息',
  `emotion_result` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '情感分析结果',
  `emotion_confidence` decimal(5, 2) NULL DEFAULT NULL COMMENT '情感置信度',
  `reply_list` json NULL COMMENT '生成的回复列表（JSON格式）',
  `selected_reply` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '用户选择的回复',
  `selected_style` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '选择的回复风格',
  `is_favorite` tinyint NULL DEFAULT 0 COMMENT '是否收藏：0-未收藏，1-已收藏',
  `user_rating` tinyint NULL DEFAULT NULL COMMENT '用户评分：1-5分',
  `user_feedback` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '用户反馈',
  `process_time` bigint NULL DEFAULT NULL COMMENT '处理耗时（毫秒）',
  `client_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '客户端IP',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '用户代理',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除标志：0-未删除，1-已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_emotion_result`(`emotion_result` ASC) USING BTREE,
  INDEX `idx_is_favorite`(`is_favorite` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE,
  INDEX `idx_user_create`(`user_id` ASC, `create_time` ASC) USING BTREE,
  CONSTRAINT `reply_history_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '回复历史表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for system_config
-- ----------------------------
DROP TABLE IF EXISTS `system_config`;
CREATE TABLE `system_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置键',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '配置值',
  `config_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '配置描述',
  `config_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'string' COMMENT '配置类型：string-字符串，number-数字，boolean-布尔，json-JSON',
  `is_public` tinyint NULL DEFAULT 0 COMMENT '是否公开：0-私有，1-公开',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `config_key`(`config_key` ASC) USING BTREE,
  INDEX `idx_config_key`(`config_key` ASC) USING BTREE,
  INDEX `idx_is_public`(`is_public` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '系统配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_settings
-- ----------------------------
DROP TABLE IF EXISTS `user_settings`;
CREATE TABLE `user_settings`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '设置ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `theme` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'light' COMMENT '主题：light-浅色，dark-深色',
  `font_size` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'medium' COMMENT '字体大小：small-小，medium-中，large-大',
  `auto_save` tinyint NULL DEFAULT 1 COMMENT '自动保存：0-关闭，1-开启',
  `show_floating_bubble` tinyint NULL DEFAULT 0 COMMENT '显示悬浮气泡：0-关闭，1-开启',
  `reply_styles` json NULL COMMENT '偏好的回复风格列表',
  `preferred_reply_count` tinyint NULL DEFAULT 2 COMMENT '偏好生成回复数量：1-3个',
  `reply_generation_mode` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'smart' COMMENT '回复生成模式：smart-智能选择，custom-自定义风格，single-单一风格',
  `primary_style` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'warm_caring' COMMENT '主要回复风格',
  `notification_enabled` tinyint NULL DEFAULT 1 COMMENT '通知开启：0-关闭，1-开启',
  `notification_sound` tinyint NULL DEFAULT 1 COMMENT '通知声音：0-关闭，1-开启',
  `notification_vibrate` tinyint NULL DEFAULT 1 COMMENT '通知震动：0-关闭，1-开启',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `user_id`(`user_id` ASC) USING BTREE,
  CONSTRAINT `user_settings_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户设置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_stats
-- ----------------------------
DROP TABLE IF EXISTS `user_stats`;
CREATE TABLE `user_stats`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `daily_usage` int NULL DEFAULT 0 COMMENT '当日使用次数',
  `daily_quota` int NULL DEFAULT 5 COMMENT '当日配额',
  `total_usage` int NULL DEFAULT 0 COMMENT '总使用次数',
  `is_vip` tinyint NULL DEFAULT 0 COMMENT '是否VIP用户',
  `created_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_date`(`user_id` ASC, `stat_date` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_stat_date`(`stat_date` ASC) USING BTREE,
  CONSTRAINT `user_stats_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户统计表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `nickname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '昵称',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机号',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码（加密后）',
  `avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像URL',
  `status` tinyint NULL DEFAULT 0 COMMENT '用户状态：0-正常，1-禁用',
  `is_vip` tinyint NULL DEFAULT 0 COMMENT '是否VIP：0-普通用户，1-VIP用户',
  `is_admin` tinyint NULL DEFAULT 0 COMMENT '是否管理员：0-普通用户，1-管理员',
  `vip_expire_time` datetime NULL DEFAULT NULL COMMENT 'VIP过期时间',
  `daily_quota` int NULL DEFAULT 5 COMMENT '每日配额',
  `today_used` int NULL DEFAULT 0 COMMENT '今日已使用次数',
  `total_used` int NULL DEFAULT 0 COMMENT '总使用次数',
  `last_login_time` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '最后登录IP',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除标志：0-未删除，1-已删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username` ASC) USING BTREE,
  INDEX `idx_username`(`username` ASC) USING BTREE,
  INDEX `idx_email`(`email` ASC) USING BTREE,
  INDEX `idx_phone`(`phone` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for version_update_logs
-- ----------------------------
DROP TABLE IF EXISTS `version_update_logs`;
CREATE TABLE `version_update_logs`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户ID',
  `device_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设备ID',
  `current_version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '当前版本',
  `target_version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '目标版本',
  `platform` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '平台类型',
  `action` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作类型：check-检查更新，download-下载，install-安装，skip-跳过',
  `result` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '结果：success-成功，failed-失败，cancelled-取消',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '错误信息',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户代理',
  `ip_address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_device_id`(`device_id` ASC) USING BTREE,
  INDEX `idx_platform`(`platform` ASC) USING BTREE,
  INDEX `idx_action`(`action` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '版本更新日志表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

-- ========================================
-- 2. 插入部分初始数据
-- ========================================

-- 插入默认用户
INSERT INTO users (username, nickname, email, password, daily_quota, status, is_admin, is_vip, vip_expire_time) VALUES
('YUMU', '情感助手管理员', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKfkiPOKOhOhJJJJJJJJJJJJJ', -1, 0, 1, 0, NULL),
('admin001', 'P', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKfkiPOKOhOhJJJJJJJJJJJJJ', -1, 0, 1, 0, NULL)


-- 插入用户设置
INSERT INTO user_settings (user_id, theme, font_size, auto_save, reply_styles, preferred_reply_count, reply_generation_mode, primary_style) VALUES
(1, 'light', 'medium', 1, '["warm_caring", "humorous", "high_eq"]', 3, 'custom', 'warm_caring'),
(2, 'dark', 'large', 1, '["mature", "direct", "high_eq"]', 3, 'custom', 'mature')

-- 插入系统配置
INSERT INTO system_config (config_key, config_value, config_desc, config_type, is_public) VALUES
('default_daily_quota', '3', '普通用户每日配额', 'number', 1),
('vip_daily_quota', '50', 'VIP用户每日配额', 'number', 1),
('admin_daily_quota', '-1', '管理员每日配额（-1表示无限制）', 'number', 1),
('max_message_length', '1000', '最大消息长度', 'number', 1),
('supported_emotions', '["开心", "难过", "愤怒", "担心", "兴奋", "平静", "关心", "感谢"]', '支持的情感类型', 'json', 1),
('supported_reply_styles', '{"warm_caring": "暖男", "humorous": "玩梗", "romantic": "撩妹", "high_eq": "高情商", "direct": "直接", "mature": "成熟稳重", "gentle": "温柔大叔", "dominant": "霸道总裁", "literary": "文艺风格", "detailed": "话痨风格"}', '支持的回复风格', 'json', 1),
('enable_user_registration', 'true', '是否开启用户注册', 'boolean', 1),
('enable_guest_access', 'true', '是否允许游客访问', 'boolean', 1),
('api_rate_limit', '60', 'API调用频率限制（每分钟）', 'number', 0),
('maintenance_mode', 'false', '维护模式', 'boolean', 0),
('app_version', '1.0.0', '应用版本', 'string', 1),
('activation_code_types', '{"vip_1d": "1天VIP", "vip_7d": "7天VIP", "vip_1m": "1个月VIP", "vip_3m": "3个月VIP", "vip_6m": "6个月VIP", "vip_1y": "1年VIP"}', '激活码类型配置', 'json', 1);