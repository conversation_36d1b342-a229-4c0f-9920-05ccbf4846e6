version: '3.8'

services:
  # 前端 Nginx 服务 (仅用于开发测试H5版本)
  frontend:
    image: nginx:alpine
    container_name: emotional-reply-frontend
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - ../uniapp-frontend/dist/build/h5:/var/www/emotional-reply/h5
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - emotional-reply-network

  # 后端 Spring Boot 服务
  backend:
    build:
      context: ../backend-service
      dockerfile: Dockerfile
    container_name: emotional-reply-backend
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=emotional_reply
      - DB_USERNAME=emotional_reply
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET=${JWT_SECRET}
    volumes:
      - ./logs/backend:/var/log/emotional-reply
      - ./config/application-prod.yml:/app/config/application-prod.yml
    depends_on:
      - mysql
      - redis
    restart: unless-stopped
    networks:
      - emotional-reply-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: emotional-reply-mysql
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=emotional_reply
      - MYSQL_USER=emotional_reply
      - MYSQL_PASSWORD=${DB_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
      - ./config/mysql.cnf:/etc/mysql/conf.d/custom.cnf
    restart: unless-stopped
    networks:
      - emotional-reply-network
    command: --default-authentication-plugin=mysql_native_password

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: emotional-reply-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf
    restart: unless-stopped
    networks:
      - emotional-reply-network
    command: redis-server /usr/local/etc/redis/redis.conf

  # 监控服务 (可选)
  prometheus:
    image: prom/prometheus
    container_name: emotional-reply-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    restart: unless-stopped
    networks:
      - emotional-reply-network

  grafana:
    image: grafana/grafana
    container_name: emotional-reply-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
    restart: unless-stopped
    networks:
      - emotional-reply-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  emotional-reply-network:
    driver: bridge
