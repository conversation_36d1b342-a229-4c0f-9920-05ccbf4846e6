{"name": "wechat-activation-bot", "version": "1.0.0", "description": "微信公众号激活码自动回复系统", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "init-db": "node scripts/init-database.js", "local-test": "node scripts/local-test.js", "local-test-interactive": "node scripts/local-test.js interactive", "db-status": "node scripts/switch-database.js status", "db-backup": "node scripts/switch-database.js backup", "db-restore": "node scripts/switch-database.js restore", "db-check": "node scripts/switch-database.js check", "generate-password-hash": "node scripts/generate-password-hash.js", "hash-password": "node scripts/hash-password.js"}, "keywords": ["wechat", "activation-code", "bot", "nodejs"], "author": "Your Name", "license": "MIT", "dependencies": {"axios": "^1.5.0", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "mysql2": "^3.6.0", "xml2js": "^0.6.2"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3"}}