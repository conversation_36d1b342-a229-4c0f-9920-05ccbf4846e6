package com.emotional.service.service;

import com.emotional.service.entity.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * VIP状态监控服务
 * 用于监控即将过期的VIP用户，在2核2G服务器上优化性能
 */
@Slf4j
@Service
public class VipMonitorService {

    @Autowired
    private UserService userService;

    // VIP过期监控容器 - 使用ConcurrentHashMap保证线程安全
    // Key: 用户ID, Value: VIP过期时间
    private final Map<Long, LocalDateTime> expiringVipUsers = new ConcurrentHashMap<>();
    
    // 监控阈值：提前多少天开始监控（默认3天）
    private static final int MONITOR_DAYS_BEFORE_EXPIRE = 3;
    
    // 容器大小限制，避免内存占用过大
    private static final int MAX_MONITOR_SIZE = 1000;

    /**
     * 服务启动时初始化监控容器
     */
    @PostConstruct
    public void initializeMonitor() {
        log.info("VIP监控服务启动，开始初始化监控容器...");
        try {
            loadExpiringVipUsers();
            log.info("VIP监控容器初始化完成，当前监控用户数: {}", expiringVipUsers.size());
        } catch (Exception e) {
            log.error("VIP监控容器初始化失败", e);
        }
    }

    /**
     * 用户登录时检查VIP状态
     * @param user 登录的用户
     */
    public void checkUserVipOnLogin(User user) {
        if (user == null || user.getVipExpireTime() == null) {
            return;
        }

        LocalDateTime expireTime = user.getVipExpireTime();
        LocalDateTime now = LocalDateTime.now();
        
        // 计算距离过期的天数
        long daysUntilExpire = ChronoUnit.DAYS.between(now, expireTime);
        
        if (daysUntilExpire <= MONITOR_DAYS_BEFORE_EXPIRE && daysUntilExpire >= 0) {
            // 添加到监控容器
            addToMonitor(user.getId(), expireTime);
            log.info("用户 {} 的VIP将在 {} 天后过期，已加入监控", user.getId(), daysUntilExpire);
        } else if (daysUntilExpire < 0) {
            // VIP已过期，立即处理
            handleVipExpired(user.getId());
        }
    }

    /**
     * 添加用户到监控容器
     * @param userId 用户ID
     * @param expireTime VIP过期时间
     */
    public void addToMonitor(Long userId, LocalDateTime expireTime) {
        // 检查容器大小限制
        if (expiringVipUsers.size() >= MAX_MONITOR_SIZE) {
            log.warn("VIP监控容器已达到最大容量 {}，跳过添加用户 {}", MAX_MONITOR_SIZE, userId);
            return;
        }
        
        expiringVipUsers.put(userId, expireTime);
    }

    /**
     * 从监控容器中移除用户
     * @param userId 用户ID
     */
    public void removeFromMonitor(Long userId) {
        expiringVipUsers.remove(userId);
        log.debug("用户 {} 已从VIP监控容器中移除", userId);
    }

    /**
     * 定时检查监控容器中的用户（每小时执行一次）
     */
    @Scheduled(fixedRate = 3600000) // 1小时 = 3600000毫秒
    public void checkExpiringUsers() {
        if (expiringVipUsers.isEmpty()) {
            log.debug("VIP监控容器为空，跳过检查");
            return;
        }

        log.info("开始检查VIP过期用户，当前监控用户数: {}", expiringVipUsers.size());
        LocalDateTime now = LocalDateTime.now();
        
        // 使用迭代器安全地遍历和修改Map
        expiringVipUsers.entrySet().removeIf(entry -> {
            Long userId = entry.getKey();
            LocalDateTime expireTime = entry.getValue();
            
            if (expireTime.isBefore(now)) {
                // VIP已过期，处理过期逻辑
                handleVipExpired(userId);
                log.info("用户 {} 的VIP已过期，已处理并从监控中移除", userId);
                return true; // 从容器中移除
            }
            return false; // 保留在容器中
        });
    }

    /**
     * 处理VIP过期的用户
     * @param userId 用户ID
     */
    private void handleVipExpired(Long userId) {
        try {
            User user = userService.getUserById(userId);
            if (user != null && user.getIsVip() == 1) {
                // 更新用户VIP状态
                user.setIsVip(0);
                user.setVipExpireTime(null);
                userService.updateUserInfo(userId, user);
                
                log.info("用户 {} 的VIP状态已更新为过期", userId);
                
                // 这里可以添加其他过期处理逻辑，比如：
                // 1. 发送过期通知
                // 2. 记录过期日志
                // 3. 触发相关业务逻辑
            }
        } catch (Exception e) {
            log.error("处理用户 {} VIP过期失败", userId, e);
        }
    }

    /**
     * 从数据库加载即将过期的VIP用户
     */
    private void loadExpiringVipUsers() {
        try {
            // 简化实现：服务启动时不预加载，依赖用户登录时动态加载
            // 这样可以避免启动时的数据库查询压力，适合2核2G服务器
            log.info("VIP监控容器初始化完成，监控阈值: {}天，将在用户登录时动态加载监控数据", MONITOR_DAYS_BEFORE_EXPIRE);

        } catch (Exception e) {
            log.error("VIP监控容器初始化失败", e);
        }
    }

    /**
     * 获取当前监控的用户数量
     * @return 监控用户数量
     */
    public int getMonitoredUserCount() {
        return expiringVipUsers.size();
    }

    /**
     * 清理监控容器（用于测试或维护）
     */
    public void clearMonitor() {
        expiringVipUsers.clear();
        log.info("VIP监控容器已清空");
    }

    /**
     * 每天凌晨2点清理过期的监控记录
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void dailyCleanup() {
        LocalDateTime now = LocalDateTime.now();
        int beforeSize = expiringVipUsers.size();
        
        // 移除已经过期很久的记录（超过1天）
        expiringVipUsers.entrySet().removeIf(entry -> 
            entry.getValue().isBefore(now.minusDays(1))
        );
        
        int afterSize = expiringVipUsers.size();
        if (beforeSize != afterSize) {
            log.info("VIP监控容器日常清理完成，清理前: {}，清理后: {}", beforeSize, afterSize);
        }
    }
}
