[2025-07-07T11:57:27.576Z] [INFO] 初始化本地测试环境...
[2025-07-07T11:57:27.581Z] [INFO] 内存数据库初始化完成（演示模式）
[2025-07-07T11:57:27.582Z] [INFO] 本地测试环境初始化完成
[2025-07-07T11:57:27.585Z] [INFO] 关键字缓存已更新，共 4 个关键字
[2025-07-07T11:57:27.586Z] [INFO] 精确匹配关键字: VIP会员 -> VIP会员
[2025-07-07T11:57:27.587Z] [INFO] 精确匹配关键字: 高级版 -> 高级版
[2025-07-07T11:57:27.588Z] [INFO] 精确匹配关键字: 专业版 -> 专业版
[2025-07-07T11:57:27.589Z] [INFO] 精确匹配关键字: 测试 -> 测试
[2025-07-07T11:57:27.591Z] [INFO] 精确匹配关键字: VIP会员 -> VIP会员
[2025-07-07T11:57:27.592Z] [INFO] 为用户 test_user_001 分配激活码: CRZ43TQZT9U9N03M
[2025-07-07T11:57:27.593Z] [INFO] 精确匹配关键字: vip会员 -> VIP会员
[2025-07-07T11:57:27.594Z] [INFO] 为用户 test_user_002 分配激活码: GPPJJDR6MYSV6SKU
[2025-07-07T11:57:27.596Z] [INFO] 未找到匹配的关键字: 不存在的关键字
[2025-07-07T11:57:27.598Z] [INFO] 精确匹配关键字: VIP会员 -> VIP会员
[2025-07-07T11:57:27.602Z] [INFO] 为用户 user001 分配激活码: OXMXNU84QF307QK0
[2025-07-07T11:57:27.604Z] [INFO] 精确匹配关键字: VIP会员 -> VIP会员
[2025-07-07T11:57:27.605Z] [INFO] 为用户 user002 分配激活码: VDYA1E26F154ASH9
[2025-07-07T11:57:27.606Z] [INFO] 精确匹配关键字: VIP会员 -> VIP会员
[2025-07-07T11:57:27.607Z] [INFO] 为用户 user003 分配激活码: YOW643R3BTL7SCN5
[2025-07-07T11:57:27.608Z] [INFO] 精确匹配关键字: VIP会员 -> VIP会员
[2025-07-07T11:57:27.609Z] [INFO] 用户 user001 已获取过关键字 1 的激活码
[2025-07-07T11:57:27.611Z] [INFO] 内存数据库连接已关闭
[2025-07-07T11:57:43.864Z] [INFO] 内存数据库初始化完成（演示模式）
[2025-07-07T12:23:41.043Z] [INFO] 内存数据库初始化完成（演示模式）
[2025-07-07T12:24:19.196Z] [INFO] 内存数据库初始化完成（演示模式）
[2025-07-07T12:24:33.241Z] [INFO] 内存数据库初始化完成（演示模式）
[2025-07-07T12:25:01.277Z] [INFO] 内存数据库初始化完成（演示模式）
[2025-07-07T12:26:08.964Z] [INFO] 内存数据库初始化完成（演示模式）
[2025-07-07T12:26:56.771Z] [INFO] 内存数据库初始化完成（演示模式）
[2025-07-07T12:26:59.971Z] [INFO] 内存数据库初始化完成（演示模式）
[2025-07-07T12:27:47.366Z] [INFO] 内存数据库初始化完成（演示模式）
[2025-07-07T12:27:47.373Z] [INFO] 所有服务初始化完成
[2025-07-07T12:27:47.382Z] [INFO] 微信激活码机器人启动成功，端口: 3000
[2025-07-07T12:29:37.484Z] [INFO] 初始化本地测试环境...
[2025-07-07T12:29:37.487Z] [INFO] 内存数据库初始化完成（演示模式）
[2025-07-07T12:29:37.489Z] [INFO] 本地测试环境初始化完成
[2025-07-07T12:29:47.003Z] [INFO] 关键字缓存已更新，共 4 个关键字
[2025-07-07T12:29:47.005Z] [INFO] 未找到匹配的关键字: emo
[2025-07-07T12:29:59.941Z] [INFO] 精确匹配关键字: VIP会员 -> VIP会员
[2025-07-07T12:29:59.946Z] [INFO] 为用户 interactive_user_1751891399940 分配激活码: DIAYWAP8RD4WQ569
[2025-07-07T12:49:21.620Z] [INFO] 内存数据库初始化完成（演示模式）
[2025-07-07T12:49:21.626Z] [INFO] 所有服务初始化完成
[2025-07-07T12:55:25.420Z] [INFO] 内存数据库初始化完成（演示模式）
[2025-07-07T12:55:25.427Z] [INFO] 所有服务初始化完成
[2025-07-07T12:55:25.433Z] [INFO] 微信激活码机器人启动成功，端口: 3001
[2025-07-07T13:02:05.909Z] [INFO] 初始化本地测试环境...
[2025-07-07T13:02:05.917Z] [INFO] 内存数据库初始化完成（演示模式）
[2025-07-07T13:02:05.919Z] [INFO] 本地测试环境初始化完成
[2025-07-07T13:02:05.920Z] [INFO] 关键字缓存已更新，共 4 个关键字
[2025-07-07T13:02:05.921Z] [INFO] 精确匹配关键字: VIP会员 -> VIP会员
[2025-07-07T13:02:05.923Z] [INFO] 精确匹配关键字: 高级版 -> 高级版
[2025-07-07T13:02:05.923Z] [INFO] 精确匹配关键字: 专业版 -> 专业版
[2025-07-07T13:02:05.925Z] [INFO] 精确匹配关键字: 测试 -> 测试
[2025-07-07T13:02:05.929Z] [INFO] 精确匹配关键字: VIP会员 -> VIP会员
[2025-07-07T13:02:05.931Z] [INFO] 为用户 test_user_001 分配激活码: KFQNK346E01RLWV6
[2025-07-07T13:02:05.933Z] [INFO] 精确匹配关键字: vip会员 -> VIP会员
[2025-07-07T13:02:05.937Z] [INFO] 为用户 test_user_002 分配激活码: 16T9EGEWETVG6ZDW
[2025-07-07T13:02:05.943Z] [INFO] 未找到匹配的关键字: 不存在的关键字
[2025-07-07T13:02:05.946Z] [INFO] 精确匹配关键字: VIP会员 -> VIP会员
[2025-07-07T13:02:05.948Z] [INFO] 为用户 user001 分配激活码: 2VIMDBO8I4Z9OS0M
[2025-07-07T13:02:05.956Z] [INFO] 精确匹配关键字: VIP会员 -> VIP会员
[2025-07-07T13:02:05.960Z] [INFO] 为用户 user002 分配激活码: 03P0RGK7LYTPYMS1
[2025-07-07T13:02:05.962Z] [INFO] 精确匹配关键字: VIP会员 -> VIP会员
[2025-07-07T13:02:05.963Z] [INFO] 为用户 user003 分配激活码: 42WTZ0JSONF9EK3Z
[2025-07-07T13:02:05.964Z] [INFO] 精确匹配关键字: VIP会员 -> VIP会员
[2025-07-07T13:02:05.965Z] [INFO] 用户 user001 已获取过关键字 1 的激活码
[2025-07-07T13:02:05.968Z] [INFO] 内存数据库连接已关闭
[2025-07-07T14:29:14.260Z] [INFO] MySQL数据库初始化完成
[2025-07-07T14:29:14.265Z] [INFO] 所有服务初始化完成
[2025-07-07T14:29:14.271Z] [INFO] 微信激活码机器人启动成功，端口: 3001
[2025-07-07T14:29:59.513Z] [INFO] 添加新关键字: emo
[2025-07-07T14:30:02.915Z] [INFO] 为关键字 1 生成了 10 个激活码
[2025-07-07T14:40:00.444Z] [INFO] MySQL数据库初始化完成
[2025-07-07T14:40:00.448Z] [INFO] 所有服务初始化完成
[2025-07-07T14:40:00.452Z] [INFO] 微信激活码机器人启动成功，端口: 3001
[2025-07-07T15:12:59.105Z] [INFO] 创建了keywords表
[2025-07-07T15:12:59.107Z] [INFO] 已连接到现有数据库: emotional_reply_db
[2025-07-07T15:12:59.109Z] [INFO] 所有服务初始化完成
[2025-07-07T15:12:59.114Z] [INFO] 微信激活码机器人启动成功，端口: 3001
[2025-07-07T16:31:01.080Z] [INFO] 已连接到激活码数据库: emotional_reply_db
[2025-07-07T16:31:01.124Z] [INFO] 已连接到关键词数据库: wechat_activation_bot
[2025-07-07T16:31:01.161Z] [INFO] 关键词表检查完成
[2025-07-07T16:31:01.201Z] [INFO] 激活码表字段: [
  "id",
  "code",
  "code_type",
  "duration_days",
  "batch_id",
  "created_by",
  "used_by",
  "used_time",
  "status",
  "remark",
  "created_time",
  "updated_time"
]
[2025-07-07T16:31:01.207Z] [INFO] 激活码表中共有 5 条记录
[2025-07-07T16:31:01.210Z] [INFO] 所有服务初始化完成
[2025-07-07T16:32:41.524Z] [INFO] 已连接到激活码数据库: emotional_reply_db
[2025-07-07T16:32:41.553Z] [INFO] 已连接到关键词数据库: wechat_activation_bot
[2025-07-07T16:32:41.557Z] [INFO] 关键词表检查完成
[2025-07-07T16:32:41.563Z] [INFO] 激活码表字段: [
  "id",
  "code",
  "code_type",
  "duration_days",
  "batch_id",
  "created_by",
  "used_by",
  "used_time",
  "status",
  "remark",
  "created_time",
  "updated_time"
]
[2025-07-07T16:32:41.570Z] [INFO] 激活码表中共有 5 条记录
[2025-07-07T16:32:41.573Z] [INFO] 所有服务初始化完成
[2025-07-07T16:32:41.577Z] [INFO] 微信激活码机器人启动成功，端口: 3001
[2025-07-07T16:35:33.487Z] [ERROR] 批量插入激活码失败: {
  "message": "Field 'code_type' doesn't have a default value",
  "code": "ER_NO_DEFAULT_FOR_FIELD",
  "errno": 1364,
  "sql": "INSERT INTO activation_codes (code) VALUES (?), (?), (?), (?), (?)",
  "sqlState": "HY000",
  "sqlMessage": "Field 'code_type' doesn't have a default value"
}
[2025-07-07T16:35:33.488Z] [ERROR] 生成激活码失败: {
  "message": "Field 'code_type' doesn't have a default value",
  "code": "ER_NO_DEFAULT_FOR_FIELD",
  "errno": 1364,
  "sql": "INSERT INTO activation_codes (code) VALUES (?), (?), (?), (?), (?)",
  "sqlState": "HY000",
  "sqlMessage": "Field 'code_type' doesn't have a default value"
}
[2025-07-07T16:35:33.489Z] [ERROR] 生成激活码失败: {
  "message": "Field 'code_type' doesn't have a default value",
  "code": "ER_NO_DEFAULT_FOR_FIELD",
  "errno": 1364,
  "sql": "INSERT INTO activation_codes (code) VALUES (?), (?), (?), (?), (?)",
  "sqlState": "HY000",
  "sqlMessage": "Field 'code_type' doesn't have a default value"
}
[2025-07-07T16:35:44.887Z] [ERROR] 批量插入激活码失败: {
  "message": "Field 'code_type' doesn't have a default value",
  "code": "ER_NO_DEFAULT_FOR_FIELD",
  "errno": 1364,
  "sql": "INSERT INTO activation_codes (code) VALUES (?), (?), (?), (?), (?)",
  "sqlState": "HY000",
  "sqlMessage": "Field 'code_type' doesn't have a default value"
}
[2025-07-07T16:35:44.888Z] [ERROR] 生成激活码失败: {
  "message": "Field 'code_type' doesn't have a default value",
  "code": "ER_NO_DEFAULT_FOR_FIELD",
  "errno": 1364,
  "sql": "INSERT INTO activation_codes (code) VALUES (?), (?), (?), (?), (?)",
  "sqlState": "HY000",
  "sqlMessage": "Field 'code_type' doesn't have a default value"
}
[2025-07-07T16:35:44.889Z] [ERROR] 生成激活码失败: {
  "message": "Field 'code_type' doesn't have a default value",
  "code": "ER_NO_DEFAULT_FOR_FIELD",
  "errno": 1364,
  "sql": "INSERT INTO activation_codes (code) VALUES (?), (?), (?), (?), (?)",
  "sqlState": "HY000",
  "sqlMessage": "Field 'code_type' doesn't have a default value"
}
[2025-07-07T16:35:50.791Z] [ERROR] 批量插入激活码失败: {
  "message": "Field 'code_type' doesn't have a default value",
  "code": "ER_NO_DEFAULT_FOR_FIELD",
  "errno": 1364,
  "sql": "INSERT INTO activation_codes (code) VALUES (?), (?), (?), (?), (?)",
  "sqlState": "HY000",
  "sqlMessage": "Field 'code_type' doesn't have a default value"
}
[2025-07-07T16:35:50.792Z] [ERROR] 生成激活码失败: {
  "message": "Field 'code_type' doesn't have a default value",
  "code": "ER_NO_DEFAULT_FOR_FIELD",
  "errno": 1364,
  "sql": "INSERT INTO activation_codes (code) VALUES (?), (?), (?), (?), (?)",
  "sqlState": "HY000",
  "sqlMessage": "Field 'code_type' doesn't have a default value"
}
[2025-07-07T16:35:50.793Z] [ERROR] 生成激活码失败: {
  "message": "Field 'code_type' doesn't have a default value",
  "code": "ER_NO_DEFAULT_FOR_FIELD",
  "errno": 1364,
  "sql": "INSERT INTO activation_codes (code) VALUES (?), (?), (?), (?), (?)",
  "sqlState": "HY000",
  "sqlMessage": "Field 'code_type' doesn't have a default value"
}
[2025-07-07T16:40:50.710Z] [INFO] 删除关键字: 1
[2025-07-07T16:41:50.330Z] [INFO] 添加新关键字: emo
[2025-07-07T16:42:02.340Z] [ERROR] 批量插入激活码失败: {
  "message": "Field 'code_type' doesn't have a default value",
  "code": "ER_NO_DEFAULT_FOR_FIELD",
  "errno": 1364,
  "sql": "INSERT INTO activation_codes (code) VALUES (?), (?), (?), (?), (?), (?), (?), (?), (?), (?)",
  "sqlState": "HY000",
  "sqlMessage": "Field 'code_type' doesn't have a default value"
}
[2025-07-07T16:42:02.341Z] [ERROR] 生成激活码失败: {
  "message": "Field 'code_type' doesn't have a default value",
  "code": "ER_NO_DEFAULT_FOR_FIELD",
  "errno": 1364,
  "sql": "INSERT INTO activation_codes (code) VALUES (?), (?), (?), (?), (?), (?), (?), (?), (?), (?)",
  "sqlState": "HY000",
  "sqlMessage": "Field 'code_type' doesn't have a default value"
}
[2025-07-07T16:42:02.342Z] [ERROR] 生成激活码失败: {
  "message": "Field 'code_type' doesn't have a default value",
  "code": "ER_NO_DEFAULT_FOR_FIELD",
  "errno": 1364,
  "sql": "INSERT INTO activation_codes (code) VALUES (?), (?), (?), (?), (?), (?), (?), (?), (?), (?)",
  "sqlState": "HY000",
  "sqlMessage": "Field 'code_type' doesn't have a default value"
}
[2025-07-07T16:48:27.262Z] [INFO] 已连接到激活码数据库: emotional_reply_db
[2025-07-07T16:48:27.275Z] [INFO] 已连接到关键词数据库: wechat_activation_bot
[2025-07-07T16:48:27.281Z] [INFO] 关键词表检查完成
[2025-07-07T16:48:27.289Z] [INFO] 激活码表字段: [
  "id",
  "code",
  "code_type",
  "duration_days",
  "batch_id",
  "created_by",
  "used_by",
  "used_time",
  "status",
  "remark",
  "created_time",
  "updated_time"
]
[2025-07-07T16:48:27.292Z] [INFO] 激活码表中共有 5 条记录
[2025-07-07T16:48:27.295Z] [INFO] 所有服务初始化完成
[2025-07-07T16:48:27.305Z] [INFO] 微信激活码机器人启动成功，端口: 3001
[2025-07-07T16:49:44.583Z] [INFO] 批量删除激活码成功: 3/3
[2025-07-07T16:59:32.909Z] [INFO] 已连接到激活码数据库: emotional_reply_db
[2025-07-07T16:59:32.925Z] [INFO] 已连接到关键词数据库: wechat_activation_bot
[2025-07-07T16:59:32.932Z] [INFO] 关键词表检查完成
[2025-07-07T16:59:32.941Z] [INFO] 激活码表字段: [
  "id",
  "code",
  "code_type",
  "duration_days",
  "batch_id",
  "created_by",
  "used_by",
  "used_time",
  "status",
  "remark",
  "created_time",
  "updated_time"
]
[2025-07-07T16:59:32.944Z] [INFO] 激活码表中共有 2 条记录
[2025-07-07T16:59:33.055Z] [INFO] 认证中间件初始化完成
[2025-07-07T16:59:33.058Z] [INFO] 所有服务初始化完成
[2025-07-07T16:59:33.065Z] [INFO] 微信激活码机器人启动成功，端口: 3001
[2025-07-07T17:03:42.029Z] [WARN] 登录失败: admin from ::1
[2025-07-07T17:03:44.519Z] [WARN] 登录失败: admin from ::1
[2025-07-07T17:05:48.604Z] [INFO] 管理员登录成功: admin from ::1
[2025-07-07T17:06:13.089Z] [INFO] 管理员登录成功: admin from ::1
[2025-07-07T17:07:29.133Z] [INFO] 管理员登出: unknown from ::1
[2025-07-07T17:07:44.157Z] [INFO] 管理员登录成功: admin from ::1
[2025-07-08T02:27:45.387Z] [INFO] 已连接到激活码数据库: emotional_reply_db
[2025-07-08T02:27:45.462Z] [INFO] 已连接到机器人数据库: wechat_reply_bot
[2025-07-08T02:27:45.566Z] [INFO] 机器人表检查完成
[2025-07-08T02:27:45.585Z] [INFO] 激活码表字段: [
  "id",
  "code",
  "code_type",
  "duration_days",
  "batch_id",
  "created_by",
  "used_by",
  "used_time",
  "status",
  "remark",
  "created_time",
  "updated_time"
]
[2025-07-08T02:27:45.589Z] [INFO] 激活码表中共有 2 条记录
[2025-07-08T02:27:45.590Z] [WARN] 使用明文密码，建议使用ADMIN_PASSWORD_HASH
[2025-07-08T02:27:45.692Z] [INFO] 认证中间件初始化完成
[2025-07-08T02:27:45.695Z] [INFO] 所有服务初始化完成
[2025-07-08T02:27:45.708Z] [INFO] 微信激活码机器人启动成功，端口: 3001
[2025-07-08T02:28:20.550Z] [INFO] 管理员登出: unknown from ::1
[2025-07-08T02:28:30.716Z] [WARN] 登录失败: 密码错误 - admin
[2025-07-08T02:28:30.717Z] [WARN] 登录失败: admin from ::1
[2025-07-08T02:28:36.096Z] [WARN] 登录失败: 密码错误 - admin
[2025-07-08T02:28:36.097Z] [WARN] 登录失败: admin from ::1
[2025-07-08T02:28:38.222Z] [WARN] 登录失败: 密码错误 - admin
[2025-07-08T02:28:38.223Z] [WARN] 登录失败: admin from ::1
[2025-07-08T02:28:42.123Z] [WARN] 登录失败: 密码错误 - admin
[2025-07-08T02:28:42.125Z] [WARN] 登录失败: admin from ::1
[2025-07-08T02:28:46.387Z] [WARN] 登录失败: 密码错误 - admin
[2025-07-08T02:28:46.387Z] [WARN] 登录失败: admin from ::1
[2025-07-08T02:32:17.975Z] [WARN] 登录失败: 密码错误 - admin
[2025-07-08T02:32:17.976Z] [WARN] 登录失败: admin from ::1
[2025-07-08T02:32:30.303Z] [WARN] 登录失败: 密码错误 - admin
[2025-07-08T02:32:30.304Z] [WARN] 登录失败: admin from ::1
[2025-07-08T02:32:38.309Z] [WARN] 登录失败: 密码错误 - admin
[2025-07-08T02:32:38.309Z] [WARN] 登录失败: admin from ::1
[2025-07-08T02:32:43.324Z] [WARN] 登录失败: 密码错误 - admin
[2025-07-08T02:32:43.325Z] [WARN] 登录失败: admin from ::1
[2025-07-08T02:32:44.053Z] [WARN] 登录失败: 密码错误 - admin
[2025-07-08T02:32:44.070Z] [WARN] 登录失败: admin from ::1
[2025-07-08T02:34:20.543Z] [WARN] 登录失败: 密码错误 - admin
[2025-07-08T02:34:20.544Z] [WARN] 登录失败: admin from ::1
[2025-07-08T02:35:16.601Z] [INFO] 已连接到激活码数据库: emotional_reply_db
[2025-07-08T02:35:16.640Z] [INFO] 已连接到机器人数据库: wechat_reply_bot
[2025-07-08T02:35:16.685Z] [INFO] 机器人表检查完成
[2025-07-08T02:35:16.698Z] [INFO] 激活码表字段: [
  "id",
  "code",
  "code_type",
  "duration_days",
  "batch_id",
  "created_by",
  "used_by",
  "used_time",
  "status",
  "remark",
  "created_time",
  "updated_time"
]
[2025-07-08T02:35:16.716Z] [INFO] 激活码表中共有 2 条记录
[2025-07-08T02:35:16.732Z] [INFO] 认证中间件初始化完成
[2025-07-08T02:35:16.813Z] [INFO] 所有服务初始化完成
[2025-07-08T02:35:16.838Z] [INFO] 微信激活码机器人启动成功，端口: 3001
[2025-07-08T02:35:42.765Z] [INFO] 管理员登录成功: admin from ::1
[2025-07-08T02:59:25.574Z] [INFO] 开始初始化数据库连接...
[2025-07-08T02:59:25.634Z] [INFO] ✅ 已连接到激活码数据库: emotional_reply_db
[2025-07-08T02:59:25.667Z] [INFO] ✅ 已连接到机器人数据库: wechat_reply_bot
[2025-07-08T02:59:25.720Z] [INFO] 机器人表检查完成
[2025-07-08T02:59:25.731Z] [INFO] 首次初始化数据库...
[2025-07-08T02:59:25.740Z] [INFO] 默认关键词已存在，跳过初始化
[2025-07-08T02:59:25.741Z] [INFO] 数据库初始化完成，版本: 1.0.0
[2025-07-08T02:59:25.748Z] [INFO] 激活码表字段: [
  "id",
  "code",
  "code_type",
  "duration_days",
  "batch_id",
  "created_by",
  "used_by",
  "used_time",
  "status",
  "remark",
  "created_time",
  "updated_time"
]
[2025-07-08T02:59:25.751Z] [INFO] 激活码表中共有 2 条记录
[2025-07-08T02:59:25.752Z] [INFO] 认证中间件初始化完成
[2025-07-08T02:59:25.754Z] [INFO] 所有服务初始化完成
[2025-07-08T02:59:54.984Z] [INFO] 开始初始化数据库连接...
[2025-07-08T02:59:55.035Z] [INFO] ✅ 已连接到激活码数据库: emotional_reply_db
[2025-07-08T02:59:55.050Z] [INFO] ✅ 已连接到机器人数据库: wechat_reply_bot
[2025-07-08T02:59:55.060Z] [INFO] 机器人表检查完成
[2025-07-08T02:59:55.067Z] [INFO] 数据库版本: 1.0.0
[2025-07-08T02:59:55.074Z] [INFO] 激活码表字段: [
  "id",
  "code",
  "code_type",
  "duration_days",
  "batch_id",
  "created_by",
  "used_by",
  "used_time",
  "status",
  "remark",
  "created_time",
  "updated_time"
]
[2025-07-08T02:59:55.076Z] [INFO] 激活码表中共有 2 条记录
[2025-07-08T02:59:55.079Z] [INFO] 认证中间件初始化完成
[2025-07-08T02:59:55.084Z] [INFO] 所有服务初始化完成
[2025-07-08T02:59:55.100Z] [INFO] 微信激活码机器人启动成功，端口: 3001
[2025-07-08T03:12:09.722Z] [INFO] 开始初始化数据库连接...
[2025-07-08T03:12:09.788Z] [INFO] ✅ 已连接到激活码数据库: emotional_reply_db
[2025-07-08T03:12:09.804Z] [INFO] ✅ 已连接到机器人数据库: wechat_reply_bot
[2025-07-08T03:12:09.819Z] [INFO] ✅ 机器人数据库表检查完成
[2025-07-08T03:12:09.830Z] [INFO] 激活码表字段: [
  "id",
  "code",
  "code_type",
  "duration_days",
  "batch_id",
  "created_by",
  "used_by",
  "used_time",
  "status",
  "remark",
  "created_time",
  "updated_time"
]
[2025-07-08T03:12:09.836Z] [INFO] 激活码表中共有 2 条记录
[2025-07-08T03:12:09.838Z] [INFO] 认证中间件初始化完成
[2025-07-08T03:12:09.840Z] [INFO] 所有服务初始化完成
[2025-07-08T03:12:09.854Z] [INFO] 微信激活码机器人启动成功，端口: 3001
[2025-07-08T03:28:50.858Z] [INFO] 🔗 开始初始化数据库连接...
[2025-07-08T03:28:50.918Z] [INFO] ✅ 已连接到激活码数据库: emotional_reply_db
[2025-07-08T03:28:50.942Z] [INFO] ✅ 已连接到机器人数据库: wechat_reply_bot
[2025-07-08T03:28:50.979Z] [INFO] ✅ 数据库表检查完成
[2025-07-08T03:28:50.980Z] [INFO] 认证中间件初始化完成
[2025-07-08T03:28:50.983Z] [INFO] 所有服务初始化完成
[2025-07-08T03:28:51.000Z] [INFO] 微信激活码机器人启动成功，端口: 3001
[2025-07-08T04:05:55.847Z] [INFO] 🔗 开始初始化数据库连接...
[2025-07-08T04:05:55.894Z] [INFO] ✅ 已连接到激活码数据库: emotional_reply_db
[2025-07-08T04:05:55.909Z] [INFO] ✅ 已连接到机器人数据库: wechat_reply_bot
[2025-07-08T04:05:55.933Z] [INFO] ✅ 数据库表检查完成
[2025-07-08T04:05:55.934Z] [INFO] 认证中间件初始化完成
[2025-07-08T04:05:55.935Z] [INFO] 所有服务初始化完成
[2025-07-08T04:05:55.947Z] [INFO] 微信激活码机器人启动成功，端口: 3001
[2025-07-08T04:06:07.459Z] [ERROR] 获取激活码统计失败: {}
[2025-07-08T04:06:07.461Z] [ERROR] 获取系统统计失败: {}
[2025-07-08T04:06:07.466Z] [ERROR] 获取激活码统计失败: {}
[2025-07-08T04:06:07.468Z] [ERROR] 获取统计信息失败: {}
[2025-07-08T04:06:17.974Z] [ERROR] 获取激活码列表失败: {}
[2025-07-08T04:23:52.052Z] [INFO] 🔗 开始初始化数据库连接...
[2025-07-08T04:23:52.106Z] [INFO] ✅ 已连接到激活码数据库: emotional_reply_db
[2025-07-08T04:23:52.126Z] [INFO] ✅ 已连接到机器人数据库: wechat_reply_bot
[2025-07-08T04:23:52.153Z] [INFO] ✅ 数据库表检查完成
[2025-07-08T04:23:52.155Z] [INFO] 认证中间件初始化完成
[2025-07-08T04:23:52.161Z] [INFO] 所有服务初始化完成
[2025-07-08T04:23:59.720Z] [INFO] 🔗 开始初始化数据库连接...
[2025-07-08T04:23:59.774Z] [INFO] ✅ 已连接到激活码数据库: emotional_reply_db
[2025-07-08T04:23:59.796Z] [INFO] ✅ 已连接到机器人数据库: wechat_reply_bot
[2025-07-08T04:23:59.815Z] [INFO] ✅ 数据库表检查完成
[2025-07-08T04:23:59.816Z] [INFO] 认证中间件初始化完成
[2025-07-08T04:23:59.819Z] [INFO] 所有服务初始化完成
[2025-07-08T04:23:59.831Z] [INFO] 微信激活码机器人启动成功，端口: 3001
[2025-07-08T04:25:44.100Z] [INFO] 管理员登出: unknown from ::1
[2025-07-08T04:26:14.963Z] [INFO] 管理员登录成功: admin from ::1
[2025-07-08T04:31:05.614Z] [INFO] 🔗 开始初始化数据库连接...
[2025-07-08T04:31:05.680Z] [INFO] ✅ 已连接到激活码数据库: emotional_reply_db
[2025-07-08T04:31:05.696Z] [INFO] ✅ 已连接到机器人数据库: wechat_reply_bot
[2025-07-08T04:31:05.724Z] [INFO] ✅ 数据库表检查完成
[2025-07-08T04:31:05.729Z] [INFO] 认证中间件初始化完成
[2025-07-08T04:31:05.733Z] [INFO] 所有服务初始化完成
[2025-07-08T04:31:11.184Z] [INFO] 管理员登出: unknown from ::1
[2025-07-08T04:31:19.016Z] [INFO] 管理员登录成功: admin from ::1
