package com.emotional.service.controller;

import com.emotional.service.common.Result;
import com.emotional.service.dto.request.ChangePasswordRequest;
import com.emotional.service.dto.request.UpdateUserProfileRequest;
import com.emotional.service.dto.request.UserLoginRequest;
import com.emotional.service.dto.request.UserRegisterRequest;
import com.emotional.service.dto.response.UserLoginResponse;
import com.emotional.service.entity.User;
import com.emotional.service.service.UserService;
import com.emotional.service.service.UserStatsService;
import com.emotional.service.service.VipService;
import com.emotional.service.service.VipMonitorService;
import com.emotional.service.entity.UserStats;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户控制器
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
@Validated
@CrossOrigin(originPatterns = "*", maxAge = 3600)
@Tag(name = "用户管理", description = "用户登录、注册、信息管理等接口")
public class UserController {
    
    private final UserService userService;
    private final UserStatsService userStatsService;
    private final VipService vipService;
    private final VipMonitorService vipMonitorService;
    
    /**
     * 用户登录
     */
    @Operation(summary = "用户登录", description = "用户名密码登录")
    @PostMapping("/login")
    public Result<UserLoginResponse> login(
            @Valid @RequestBody UserLoginRequest request,
            HttpServletRequest httpRequest) {
        
        log.info("用户登录请求: {}", request.getUsername());
        
        try {
            // 获取客户端信息
            String clientIp = getClientIp(httpRequest);
            String userAgent = httpRequest.getHeader("User-Agent");
            
            // 执行登录
            UserLoginResponse response = userService.login(request, clientIp, userAgent);

            // 登录成功后，检查用户VIP状态并加入监控
            try {
                User user = userService.getUserByUsername(request.getUsername());
                if (user != null) {
                    vipMonitorService.checkUserVipOnLogin(user);
                }
            } catch (Exception e) {
                log.warn("VIP状态检查失败，但不影响登录: {}", e.getMessage());
            }

            log.info("用户登录成功: {}", request.getUsername());

            return Result.success("登录成功", response);
            
        } catch (Exception e) {
            log.error("用户登录失败: {}", request.getUsername(), e);
            return Result.error("登录失败: " + e.getMessage());
        }
    }

    /**
     * 用户注册
     */
    @Operation(summary = "用户注册", description = "新用户注册")
    @PostMapping("/register")
    public Result<UserLoginResponse> register(
            @Valid @RequestBody UserRegisterRequest request,
            HttpServletRequest httpRequest) {

        log.info("用户注册请求: {}", request.getUsername());

        try {
            // 验证密码一致性
            if (!request.isPasswordMatch()) {
                return Result.error("两次密码输入不一致");
            }

            // 验证是否同意协议
            if (request.getAgreeTerms() == null || !request.getAgreeTerms()) {
                return Result.error("请同意用户协议");
            }

            // 验证是否提供联系方式
            if (!request.hasContactInfo()) {
                return Result.error("请提供邮箱或手机号");
            }

            // 执行注册
            User user = userService.register(request);

            // 获取客户端信息
            String clientIp = getClientIp(httpRequest);
            String userAgent = httpRequest.getHeader("User-Agent");

            // 更新登录信息
            userService.updateLoginInfo(user.getId(), clientIp, userAgent);

            // 生成登录响应
            UserLoginResponse response = new UserLoginResponse();
            response.setToken(userService.generateToken(user));
            response.setTokenType("Bearer");
            response.setExpiresIn(86400L);

            // 设置用户信息
            UserLoginResponse.UserInfo userInfo = new UserLoginResponse.UserInfo();
            userInfo.setId(user.getId());
            userInfo.setUsername(user.getUsername());
            userInfo.setNickname(user.getNickname());
            userInfo.setEmail(user.getEmail());
            userInfo.setPhone(user.getPhone());
            userInfo.setAvatar(user.getAvatar());
            userInfo.setIsVip(user.getIsVip() != null && user.getIsVip() == 1);
            userInfo.setIsAdmin(user.getIsAdmin() != null && user.getIsAdmin() == 1);
            userInfo.setVipExpireTime(user.getVipExpireTime());
            userInfo.setDailyQuota(user.getDailyQuota());
            userInfo.setTodayUsed(user.getTodayUsed());
            userInfo.setTotalUsed(user.getTotalUsed());
            userInfo.setStatus(user.getStatus());
            response.setUserInfo(userInfo);

            log.info("用户注册成功: {}", request.getUsername());

            return Result.success("注册成功", response);

        } catch (Exception e) {
            log.error("用户注册失败: {}", request.getUsername(), e);
            return Result.error("注册失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户信息
     */
    @Operation(summary = "获取用户信息", description = "根据用户ID获取用户详细信息")
    @GetMapping("/info/{userId}")
    public Result<User> getUserInfo(@PathVariable Long userId) {
        
        log.info("获取用户信息请求: {}", userId);
        
        try {
            User user = userService.getUserById(userId);
            
            if (user == null) {
                return Result.error("用户不存在");
            }
            
            // 清除敏感信息
            user.setPassword(null);
            
            return Result.success("获取成功", user);
            
        } catch (Exception e) {
            log.error("获取用户信息失败: {}", userId, e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新用户信息
     */
    @Operation(summary = "更新用户信息", description = "更新用户基本信息")
    @PutMapping("/info/{userId}")
    public Result<String> updateUserInfo(
            @PathVariable Long userId,
            @RequestBody User userInfo) {
        
        log.info("更新用户信息请求: {}", userId);
        
        try {
            boolean success = userService.updateUserInfo(userId, userInfo);
            
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
            
        } catch (Exception e) {
            log.error("更新用户信息失败: {}", userId, e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }
    


    /**
     * 发送注册验证码
     */
    @Operation(summary = "发送注册验证码", description = "向邮箱发送注册验证码")
    @PostMapping("/send-register-code")
    public Result<Map<String, Object>> sendRegisterCode(@RequestBody Map<String, String> request) {

        String email = request.get("email");
        log.info("发送注册验证码请求: {}", email);

        try {
            // 验证邮箱格式
            if (email == null || email.trim().isEmpty()) {
                return Result.error("邮箱不能为空");
            }

            // 检查邮箱是否已注册
            if (userService.checkUserExists(email)) {
                return Result.error("该邮箱已被注册");
            }

            // 发送验证码
            boolean sent = userService.sendRegisterCode(email);

            if (sent) {
                Map<String, Object> data = new HashMap<>();
                data.put("expire_minutes", 5);
                return Result.success("验证码已发送到您的邮箱，请查收", data);
            } else {
                return Result.error("验证码发送失败，请稍后重试");
            }

        } catch (Exception e) {
            log.error("发送注册验证码失败: {}", email, e);
            return Result.error("发送失败: " + e.getMessage());
        }
    }

    /**
     * 验证注册验证码
     */
    @Operation(summary = "验证注册验证码", description = "验证注册验证码是否正确")
    @PostMapping("/verify-register-code")
    public Result<Map<String, Object>> verifyRegisterCode(@RequestBody Map<String, String> request) {

        String email = request.get("email");
        String code = request.get("code");
        log.info("验证注册验证码请求: {}", email);

        try {
            boolean valid = userService.verifyRegisterCode(email, code);

            Map<String, Object> data = new HashMap<>();
            data.put("success", valid);
            data.put("message", valid ? "验证成功" : "验证码错误或已过期");

            return Result.success("验证完成", data);

        } catch (Exception e) {
            log.error("验证注册验证码失败: {}", email, e);
            return Result.error("验证失败: " + e.getMessage());
        }
    }

    /**
     * 检查用户是否存在
     */
    @Operation(summary = "检查用户是否存在", description = "根据手机号或邮箱检查用户是否存在")
    @PostMapping("/check-exists")
    public Result<CheckUserExistsResponse> checkUserExists(@RequestBody CheckUserExistsRequest request) {

        log.info("检查用户是否存在请求: {}", request.getIdentifier());

        try {
            boolean exists = userService.checkUserExists(request.getIdentifier());

            CheckUserExistsResponse response = new CheckUserExistsResponse();
            response.setExists(exists);

            return Result.success("检查完成", response);

        } catch (Exception e) {
            log.error("检查用户是否存在失败: {}", request.getIdentifier(), e);
            return Result.error("检查失败: " + e.getMessage());
        }
    }

    /**
     * 发送重置密码验证码
     */
    @Operation(summary = "发送重置密码验证码", description = "向用户手机或邮箱发送重置密码验证码")
    @PostMapping("/send-reset-code")
    public Result<String> sendResetPasswordCode(@RequestBody SendResetCodeRequest request) {

        log.info("发送重置密码验证码请求: {}", request.getIdentifier());

        try {
            boolean success = userService.sendResetPasswordCode(request.getIdentifier());

            if (success) {
                return Result.success("验证码发送成功");
            } else {
                return Result.error("验证码发送失败");
            }

        } catch (Exception e) {
            log.error("发送重置密码验证码失败: {}", request.getIdentifier(), e);
            return Result.error("发送失败: " + e.getMessage());
        }
    }

    /**
     * 验证重置密码验证码
     */
    @Operation(summary = "验证重置密码验证码", description = "验证重置密码验证码是否正确")
    @PostMapping("/verify-reset-code")
    public Result<VerifyResetCodeResponse> verifyResetCode(@RequestBody VerifyResetCodeRequest request) {

        log.info("验证重置密码验证码请求: {}", request.getIdentifier());

        try {
            boolean valid = userService.verifyResetCode(request.getIdentifier(), request.getCode());

            VerifyResetCodeResponse response = new VerifyResetCodeResponse();
            response.setSuccess(valid);
            response.setMessage(valid ? "验证成功" : "验证码错误或已过期");

            return Result.success("验证完成", response);

        } catch (Exception e) {
            log.error("验证重置密码验证码失败: {}", request.getIdentifier(), e);
            return Result.error("验证失败: " + e.getMessage());
        }
    }

    /**
     * 重置密码
     */
    @Operation(summary = "重置密码", description = "使用验证码重置用户密码")
    @PostMapping("/reset-password")
    public Result<ResetPasswordResponse> resetPassword(@RequestBody ResetPasswordRequest request) {

        log.info("重置密码请求: {}", request.getIdentifier());

        try {
            boolean success = userService.resetPassword(request.getIdentifier(), request.getCode(), request.getNewPassword());

            ResetPasswordResponse response = new ResetPasswordResponse();
            response.setSuccess(success);
            response.setMessage(success ? "密码重置成功" : "重置失败");

            return Result.success("重置完成", response);

        } catch (Exception e) {
            log.error("重置密码失败: {}", request.getIdentifier(), e);
            return Result.error("重置失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户资料
     */
    @Operation(summary = "更新用户资料", description = "更新用户的个人资料信息")
    @PutMapping("/profile/{userId}")
    public Result<User> updateUserProfile(
            @PathVariable Long userId,
            @Valid @RequestBody UpdateUserProfileRequest request) {

        log.info("更新用户资料请求: userId={}, request={}", userId, request);

        try {
            User updatedUser = userService.updateUserProfile(userId, request);

            // 清除敏感信息
            updatedUser.setPassword(null);

            return Result.success("资料更新成功", updatedUser);

        } catch (Exception e) {
            log.error("更新用户资料失败: userId={}", userId, e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 修改密码
     */
    @Operation(summary = "修改密码", description = "修改用户登录密码")
    @PutMapping("/change-password/{userId}")
    public Result<String> changeUserPassword(
            @PathVariable Long userId,
            @Valid @RequestBody ChangePasswordRequest request) {

        log.info("修改密码请求: userId={}", userId);

        try {
            boolean success = userService.changeUserPassword(userId, request);

            if (success) {
                return Result.success("密码修改成功");
            } else {
                return Result.error("密码修改失败");
            }

        } catch (Exception e) {
            log.error("修改密码失败: userId={}", userId, e);
            return Result.error("修改失败: " + e.getMessage());
        }
    }


    
    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 获取用户统计信息
     */
    @Operation(summary = "获取用户统计信息", description = "获取用户的使用统计数据")
    @GetMapping("/stats/{userId}")
    public Result<UserStats> getUserStats(@PathVariable Long userId) {
        log.info("获取用户统计信息: userId={}", userId);

        try {
            UserStats stats = userStatsService.getUserStats(userId);
            return Result.success("获取成功", stats);
        } catch (Exception e) {
            log.error("获取用户统计信息失败: userId={}", userId, e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 增加用户使用次数
     */
    @Operation(summary = "增加用户使用次数", description = "用户使用功能时调用此接口")
    @PostMapping("/usage/{userId}")
    public Result<String> incrementUsage(@PathVariable Long userId) {
        log.info("增加用户使用次数: userId={}", userId);

        try {
            // 检查是否还有配额
            if (!userStatsService.hasQuota(userId)) {
                return Result.error("今日使用次数已达上限");
            }

            boolean success = userStatsService.incrementUsage(userId);
            if (success) {
                return Result.success("使用次数增加成功");
            } else {
                return Result.error("使用次数增加失败");
            }
        } catch (Exception e) {
            log.error("增加用户使用次数失败: userId={}", userId, e);
            return Result.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 检查用户配额
     */
    @Operation(summary = "检查用户配额", description = "检查用户是否还有可用配额")
    @GetMapping("/quota/{userId}")
    public Result<Map<String, Object>> checkQuota(@PathVariable Long userId) {
        log.info("检查用户配额: userId={}", userId);

        try {
            UserStats stats = userStatsService.getUserStats(userId);
            boolean hasQuota = userStatsService.hasQuota(userId);

            Map<String, Object> result = new HashMap<>();
            result.put("hasQuota", hasQuota);
            result.put("dailyUsage", stats.getDailyUsage());
            result.put("dailyQuota", stats.getDailyQuota());

            // 处理无限制配额的情况
            if (stats.getDailyQuota() == -1) {
                result.put("remainingQuota", -1); // -1表示无限制
                result.put("isUnlimited", true);
            } else {
                result.put("remainingQuota", stats.getDailyQuota() - stats.getDailyUsage());
                result.put("isUnlimited", false);
            }

            result.put("totalUsage", userStatsService.getTotalUsage(userId));

            return Result.success("检查成功", result);
        } catch (Exception e) {
            log.error("检查用户配额失败: userId={}", userId, e);
            return Result.error("检查失败: " + e.getMessage());
        }
    }

    /**
     * 激活VIP
     */
    @Operation(summary = "激活VIP", description = "使用激活码激活VIP会员")
    @PostMapping("/activate-vip")
    public Result<Map<String, Object>> activateVip(@RequestBody ActivateVipRequest request) {
        log.info("VIP激活请求: userId={}, code={}", request.getUserId(), request.getActivationCode());

        try {
            Map<String, Object> result = vipService.activateVip(request.getUserId(), request.getActivationCode());

            if ((Boolean) result.get("success")) {
                return Result.success((String) result.get("message"), (Map<String, Object>) result.get("data"));
            } else {
                return Result.error((String) result.get("message"));
            }
        } catch (Exception e) {
            log.error("VIP激活失败: userId={}, code={}", request.getUserId(), request.getActivationCode(), e);
            return Result.error("激活失败: " + e.getMessage());
        }
    }

    /**
     * 检查VIP状态
     */
    @Operation(summary = "检查VIP状态", description = "检查用户当前VIP状态")
    @GetMapping("/vip-status")
    public Result<Map<String, Object>> checkVipStatus(@RequestParam Long userId) {
        try {
            User user = userService.getUserById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 检查VIP状态（这里可能会更新用户数据）
            boolean isVip = vipService.isUserVip(userId);

            // 重新获取用户信息，确保数据是最新的
            User updatedUser = userService.getUserById(userId);

            Map<String, Object> data = new HashMap<>();
            data.put("isVip", isVip);
            data.put("vipExpireTime", updatedUser.getVipExpireTime());
            data.put("dailyQuota", updatedUser.getDailyQuota());

            return Result.success("VIP状态查询成功", data);

        } catch (Exception e) {
            log.error("检查VIP状态失败: userId={}", userId, e);
            return Result.error("检查失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户配额信息
     */
    @Operation(summary = "获取用户配额", description = "获取用户当前配额信息")
    @GetMapping("/quota")
    public Result<Map<String, Object>> getUserQuota(@RequestParam Long userId) {
        try {
            UserStats stats = userStatsService.getUserStats(userId);
            if (stats == null) {
                return Result.error("用户统计信息不存在");
            }

            Map<String, Object> data = new HashMap<>();
            data.put("dailyQuota", stats.getDailyQuota());
            data.put("usedQuota", stats.getDailyUsage());
            data.put("remainingQuota", Math.max(0, stats.getDailyQuota() - stats.getDailyUsage()));
            data.put("isVip", stats.getIsVip());

            return Result.success("配额信息查询成功", data);

        } catch (Exception e) {
            log.error("获取用户配额失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 检查用户是否存在请求类
     */
    public static class CheckUserExistsRequest {
        private String identifier;

        public String getIdentifier() {
            return identifier;
        }

        public void setIdentifier(String identifier) {
            this.identifier = identifier;
        }
    }

    /**
     * 检查用户是否存在响应类
     */
    public static class CheckUserExistsResponse {
        private boolean exists;

        public boolean isExists() {
            return exists;
        }

        public void setExists(boolean exists) {
            this.exists = exists;
        }
    }

    /**
     * 发送重置密码验证码请求类
     */
    public static class SendResetCodeRequest {
        private String identifier;

        public String getIdentifier() {
            return identifier;
        }

        public void setIdentifier(String identifier) {
            this.identifier = identifier;
        }
    }

    /**
     * 验证重置密码验证码请求类
     */
    public static class VerifyResetCodeRequest {
        private String identifier;
        private String code;

        public String getIdentifier() {
            return identifier;
        }

        public void setIdentifier(String identifier) {
            this.identifier = identifier;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }
    }

    /**
     * 验证重置密码验证码响应类
     */
    public static class VerifyResetCodeResponse {
        private boolean success;
        private String message;

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }
    }

    /**
     * 重置密码请求类
     */
    public static class ResetPasswordRequest {
        private String identifier;
        private String code;
        private String newPassword;

        public String getIdentifier() {
            return identifier;
        }

        public void setIdentifier(String identifier) {
            this.identifier = identifier;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getNewPassword() {
            return newPassword;
        }

        public void setNewPassword(String newPassword) {
            this.newPassword = newPassword;
        }
    }

    /**
     * 重置密码响应类
     */
    public static class ResetPasswordResponse {
        private boolean success;
        private String message;

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }
    }

    /**
     * 激活VIP请求类
     */
    public static class ActivateVipRequest {
        private Long userId;
        private String activationCode;

        public Long getUserId() {
            return userId;
        }

        public void setUserId(Long userId) {
            this.userId = userId;
        }

        public String getActivationCode() {
            return activationCode;
        }

        public void setActivationCode(String activationCode) {
            this.activationCode = activationCode;
        }
    }
}
