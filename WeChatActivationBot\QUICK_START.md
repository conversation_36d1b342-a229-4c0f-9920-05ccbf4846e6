# 快速启动指南

## 1. 环境准备

确保您的系统已安装：
- Node.js (版本 14 或更高)
- npm 或 yarn

## 2. 项目设置

```bash
# 进入项目目录
cd WeChatActivationBot

# 安装依赖
npm install

# 复制环境变量配置文件
cp .env.example .env
```

## 3. 配置微信公众号

编辑 `.env` 文件，填入您的微信公众号信息：

```env
# 微信公众号配置
WECHAT_TOKEN=your_unique_token_here
WECHAT_APPID=your_app_id_here
WECHAT_APPSECRET=your_app_secret_here

# 服务器配置
PORT=3000
NODE_ENV=development
```

## 4. 初始化数据库

```bash
# 初始化数据库并创建示例数据
npm run init-db init
```

## 5. 启动服务

```bash
# 开发模式（自动重启）
npm run dev

# 或者生产模式
npm start
```

服务启动后，您会看到类似输出：
```
[2024-01-01T00:00:00.000Z] [INFO] 数据库初始化完成
[2024-01-01T00:00:00.000Z] [INFO] 所有服务初始化完成
[2024-01-01T00:00:00.000Z] [INFO] 微信激活码机器人启动成功，端口: 3000
```

## 6. 配置微信公众号服务器

1. 登录微信公众平台 (https://mp.weixin.qq.com)
2. 进入"开发" -> "基本配置"
3. 设置服务器配置：
   - **URL**: `http://your-domain.com/wechat`
   - **Token**: 与 `.env` 中的 `WECHAT_TOKEN` 一致
   - **消息加解密方式**: 明文模式

## 7. 测试系统

### 方法1：通过微信公众号测试
1. 关注您的微信公众号
2. 发送消息 "VIP会员"
3. 应该收到激活码回复

### 方法2：通过API测试
```bash
# 检查服务状态
curl http://localhost:3000/health

# 查看关键字列表
curl http://localhost:3000/admin/keywords

# 查看统计信息
curl http://localhost:3000/admin/stats
```

## 8. 管理激活码

### 添加新关键字
```bash
curl -X POST http://localhost:3000/admin/keywords \
  -H "Content-Type: application/json" \
  -d '{"keyword": "新产品", "description": "新产品激活码"}'
```

### 为关键字生成激活码
```bash
curl -X POST http://localhost:3000/admin/keywords/1/codes/generate \
  -H "Content-Type: application/json" \
  -d '{"count": 50}'
```

### 查看激活码统计
```bash
curl http://localhost:3000/admin/keywords/1/stats
```

## 9. 常见问题

### Q: 微信验证失败
A: 检查以下几点：
- Token 配置是否正确
- 服务器 URL 是否可以从外网访问
- 防火墙是否开放了对应端口

### Q: 数据库初始化失败
A: 确保：
- 有足够的磁盘空间
- 对 `data` 目录有写权限
- SQLite3 依赖安装正确

### Q: 激活码生成失败
A: 检查：
- 关键字是否存在
- 数据库连接是否正常
- 查看日志文件 `logs/app.log`

## 10. 生产部署

### 使用 PM2
```bash
npm install -g pm2
pm2 start src/app.js --name "wechat-bot"
pm2 startup
pm2 save
```

### 使用 Docker
```bash
docker build -t wechat-activation-bot .
docker run -d -p 3000:3000 --name wechat-bot wechat-activation-bot
```

### 使用 Docker Compose
```bash
docker-compose up -d
```

## 11. 监控和维护

### 查看日志
```bash
# 应用日志
tail -f logs/app.log

# PM2 日志
pm2 logs wechat-bot

# Docker 日志
docker logs wechat-bot
```

### 数据库维护
```bash
# 查看统计信息
node scripts/init-database.js stats

# 重置数据库（谨慎使用）
node scripts/init-database.js reset
```

## 12. 下一步

- 配置 HTTPS（生产环境必需）
- 设置监控和告警
- 备份数据库
- 优化性能配置
- 添加更多自定义功能

如需更详细的文档，请查看 `README.md` 文件。
