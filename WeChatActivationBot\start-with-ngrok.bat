@echo off
echo 启动微信公众号机器人...
echo.

echo 1. 启动Node.js服务...
start "WeChatBot" cmd /k "cd /d %~dp0 && node src/app.js"

echo.
echo 2. 等待5秒让服务启动...
timeout /t 5 /nobreak >nul

echo.
echo 3. 启动ngrok内网穿透...
echo    请确保已安装ngrok并配置authtoken
echo.
echo    如果没有安装ngrok，请：
echo    1. 访问 https://ngrok.com/
echo    2. 注册账号并下载ngrok
echo    3. 运行: ngrok config add-authtoken YOUR_TOKEN
echo.

start "ngrok" cmd /k "ngrok http 3001"

echo.
echo 4. 配置说明：
echo    - 等待ngrok启动完成
echo    - 复制ngrok提供的https URL
echo    - 在微信公众平台配置：
echo      URL: https://xxxxx.ngrok.io/wechat
echo      Token: ucan2025_fighting_yumu_2025
echo.

pause
