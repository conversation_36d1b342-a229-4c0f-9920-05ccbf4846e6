const express = require('express');
const Logger = require('../utils/logger');

class AdminRoutes {
    constructor(databaseService, keywordMatcher, activationCodeManager) {
        this.db = databaseService;
        this.keywordMatcher = keywordMatcher;
        this.activationCodeManager = activationCodeManager;
        this.router = express.Router();
        this.setupRoutes();
    }

    setupRoutes() {
        // 获取所有关键字
        this.router.get('/keywords', async (req, res) => {
            try {
                const keywords = await this.db.getAllKeywords();
                res.json({ success: true, data: keywords });
            } catch (error) {
                Logger.error('获取关键字列表失败:', error);
                res.status(500).json({ success: false, message: '获取关键字列表失败' });
            }
        });

        // 添加关键字
        this.router.post('/keywords', async (req, res) => {
            try {
                const { keyword, description } = req.body;
                
                if (!keyword) {
                    return res.status(400).json({ success: false, message: '关键字不能为空' });
                }

                const newKeyword = await this.keywordMatcher.addKeyword(keyword, description);
                res.json({ success: true, data: newKeyword });
            } catch (error) {
                Logger.error('添加关键字失败:', error);
                res.status(500).json({ success: false, message: '添加关键字失败' });
            }
        });

        // 删除关键字
        this.router.delete('/keywords/:id', async (req, res) => {
            try {
                const { id } = req.params;
                await this.keywordMatcher.removeKeyword(parseInt(id));
                res.json({ success: true, message: '关键字删除成功' });
            } catch (error) {
                Logger.error('删除关键字失败:', error);
                res.status(500).json({ success: false, message: '删除关键字失败' });
            }
        });

        // 获取关键字的激活码
        this.router.get('/keywords/:id/codes', async (req, res) => {
            try {
                const { id } = req.params;
                const { page = 1, limit = 50, status } = req.query;
                
                let codes = await this.db.getActivationCodesByKeyword(parseInt(id));
                
                // 根据状态过滤
                if (status === 'used') {
                    codes = codes.filter(code => code.used_by);
                } else if (status === 'available') {
                    codes = codes.filter(code => !code.used_by);
                }
                
                // 分页
                const startIndex = (page - 1) * limit;
                const endIndex = startIndex + parseInt(limit);
                const paginatedCodes = codes.slice(startIndex, endIndex);
                
                res.json({
                    success: true,
                    data: {
                        codes: paginatedCodes,
                        total: codes.length,
                        page: parseInt(page),
                        limit: parseInt(limit)
                    }
                });
            } catch (error) {
                Logger.error('获取激活码列表失败:', error);
                res.status(500).json({ success: false, message: '获取激活码列表失败' });
            }
        });

        // 生成激活码
        this.router.post('/keywords/:id/codes/generate', async (req, res) => {
            try {
                const { id } = req.params;
                const { count = 10, length } = req.body;
                
                if (count > 1000) {
                    return res.status(400).json({ success: false, message: '单次生成数量不能超过1000个' });
                }

                const codes = await this.activationCodeManager.generateActivationCodes(
                    parseInt(id), 
                    parseInt(count), 
                    length
                );
                
                res.json({ success: true, data: codes });
            } catch (error) {
                Logger.error('生成激活码失败:', error);
                res.status(500).json({ success: false, message: '生成激活码失败' });
            }
        });

        // 批量删除激活码（仅删除已使用的）
        this.router.delete('/keywords/:id/codes/batch-delete', async (req, res) => {
            try {
                const { id } = req.params;
                const { codeIds } = req.body;

                if (!codeIds || !Array.isArray(codeIds) || codeIds.length === 0) {
                    return res.status(400).json({ success: false, message: '请提供要删除的激活码ID列表' });
                }

                let deletedCount = 0;
                let errors = [];

                for (const codeId of codeIds) {
                    try {
                        // 先检查激活码是否已使用
                        const codes = await this.db.getAllActivationCodes();
                        const code = codes.find(c => c.id == codeId);

                        if (!code) {
                            errors.push(`激活码ID ${codeId} 不存在`);
                            continue;
                        }

                        if (!code.used_by) {
                            errors.push(`激活码 ${code.code} 未使用，不能删除`);
                            continue;
                        }

                        // 删除已使用的激活码
                        await this.db.removeActivationCode(codeId);
                        deletedCount++;

                    } catch (error) {
                        errors.push(`删除激活码ID ${codeId} 失败: ${error.message}`);
                    }
                }

                const result = {
                    deletedCount,
                    totalRequested: codeIds.length,
                    errors
                };

                if (deletedCount > 0) {
                    Logger.info(`批量删除激活码成功: ${deletedCount}/${codeIds.length}`);
                    res.json({
                        success: true,
                        message: `成功删除 ${deletedCount} 个激活码`,
                        data: result
                    });
                } else {
                    res.status(400).json({
                        success: false,
                        message: '没有激活码被删除',
                        data: result
                    });
                }

            } catch (error) {
                Logger.error('批量删除激活码失败:', error);
                res.status(500).json({ success: false, message: '批量删除激活码失败' });
            }
        });

        // 获取激活码统计
        this.router.get('/keywords/:id/stats', async (req, res) => {
            try {
                const { id } = req.params;
                const stats = await this.activationCodeManager.getActivationCodeStats(parseInt(id));
                res.json({ success: true, data: stats });
            } catch (error) {
                Logger.error('获取统计信息失败:', error);
                res.status(500).json({ success: false, message: '获取统计信息失败' });
            }
        });

        // 系统统计信息
        this.router.get('/stats', async (req, res) => {
            try {
                const keywords = await this.db.getAllKeywords();
                const activationStats = await this.db.getActivationCodeStats();
                const userStats = await this.db.getUserStats();

                const stats = {
                    totalKeywords: keywords.length,
                    activationCodes: activationStats,
                    users: userStats,
                    keywordStats: []
                };

                for (const keyword of keywords) {
                    const keywordStats = await this.activationCodeManager.getActivationCodeStats(keyword.id);
                    stats.keywordStats.push({
                        keyword: keyword.keyword,
                        ...keywordStats
                    });
                }

                res.json({ success: true, data: stats });
            } catch (error) {
                Logger.error('获取系统统计失败:', error);
                res.status(500).json({ success: false, message: '获取系统统计失败' });
            }
        });

        // ==================== 用户管理接口 ====================

        // 获取所有用户
        this.router.get('/users', async (req, res) => {
            try {
                const page = parseInt(req.query.page) || 1;
                const limit = parseInt(req.query.limit) || 20;
                const search = req.query.search || '';

                const users = await this.db.getAllUsers(page, limit, search);
                const total = await this.db.getUserCount(search);

                res.json({
                    success: true,
                    data: {
                        users,
                        pagination: {
                            page,
                            limit,
                            total,
                            pages: Math.ceil(total / limit)
                        }
                    }
                });
            } catch (error) {
                Logger.error('获取用户列表失败:', error);
                res.status(500).json({ success: false, message: '获取用户列表失败' });
            }
        });

        // 获取用户详情
        this.router.get('/users/:openid', async (req, res) => {
            try {
                const { openid } = req.params;
                const user = await this.db.getUserByOpenid(openid);

                if (!user) {
                    return res.status(404).json({ success: false, message: '用户不存在' });
                }

                res.json({
                    success: true,
                    data: user
                });
            } catch (error) {
                Logger.error('获取用户详情失败:', error);
                res.status(500).json({ success: false, message: '获取用户详情失败' });
            }
        });
    }

    getRouter() {
        return this.router;
    }
}

module.exports = AdminRoutes;
