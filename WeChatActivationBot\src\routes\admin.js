const express = require('express');
const router = express.Router();
const Logger = require('../utils/logger');

class AdminRoutes {
    constructor(databaseService, keywordMatcher, activationCodeManager) {
        this.db = databaseService;
        this.keywordMatcher = keywordMatcher;
        this.activationCodeManager = activationCodeManager;
        this.setupRoutes();
    }

    setupRoutes() {
        // 获取所有关键字
        router.get('/keywords', async (req, res) => {
            try {
                const keywords = await this.db.getAllKeywords();
                res.json({ success: true, data: keywords });
            } catch (error) {
                Logger.error('获取关键字列表失败:', error);
                res.status(500).json({ success: false, message: '获取关键字列表失败' });
            }
        });

        // 添加关键字
        router.post('/keywords', async (req, res) => {
            try {
                const { keyword, description } = req.body;
                
                if (!keyword) {
                    return res.status(400).json({ success: false, message: '关键字不能为空' });
                }

                const newKeyword = await this.keywordMatcher.addKeyword(keyword, description);
                res.json({ success: true, data: newKeyword });
            } catch (error) {
                Logger.error('添加关键字失败:', error);
                res.status(500).json({ success: false, message: '添加关键字失败' });
            }
        });

        // 删除关键字
        router.delete('/keywords/:id', async (req, res) => {
            try {
                const { id } = req.params;
                await this.keywordMatcher.removeKeyword(parseInt(id));
                res.json({ success: true, message: '关键字删除成功' });
            } catch (error) {
                Logger.error('删除关键字失败:', error);
                res.status(500).json({ success: false, message: '删除关键字失败' });
            }
        });

        // 获取关键字的激活码
        router.get('/keywords/:id/codes', async (req, res) => {
            try {
                const { id } = req.params;
                const { page = 1, limit = 50, status } = req.query;
                
                let codes = await this.db.getActivationCodesByKeyword(parseInt(id));
                
                // 根据状态过滤
                if (status === 'used') {
                    codes = codes.filter(code => code.used_by);
                } else if (status === 'available') {
                    codes = codes.filter(code => !code.used_by);
                }
                
                // 分页
                const startIndex = (page - 1) * limit;
                const endIndex = startIndex + parseInt(limit);
                const paginatedCodes = codes.slice(startIndex, endIndex);
                
                res.json({
                    success: true,
                    data: {
                        codes: paginatedCodes,
                        total: codes.length,
                        page: parseInt(page),
                        limit: parseInt(limit)
                    }
                });
            } catch (error) {
                Logger.error('获取激活码列表失败:', error);
                res.status(500).json({ success: false, message: '获取激活码列表失败' });
            }
        });

        // 生成激活码
        router.post('/keywords/:id/codes/generate', async (req, res) => {
            try {
                const { id } = req.params;
                const { count = 10, length } = req.body;
                
                if (count > 1000) {
                    return res.status(400).json({ success: false, message: '单次生成数量不能超过1000个' });
                }

                const codes = await this.activationCodeManager.generateActivationCodes(
                    parseInt(id), 
                    parseInt(count), 
                    length
                );
                
                res.json({ success: true, data: codes });
            } catch (error) {
                Logger.error('生成激活码失败:', error);
                res.status(500).json({ success: false, message: '生成激活码失败' });
            }
        });

        // 导入激活码
        router.post('/keywords/:id/codes/import', async (req, res) => {
            try {
                const { id } = req.params;
                const { codes } = req.body;
                
                if (!Array.isArray(codes) || codes.length === 0) {
                    return res.status(400).json({ success: false, message: '激活码列表不能为空' });
                }

                const result = await this.activationCodeManager.importActivationCodes(
                    parseInt(id), 
                    codes
                );
                
                res.json({ success: true, data: result });
            } catch (error) {
                Logger.error('导入激活码失败:', error);
                res.status(500).json({ success: false, message: error.message || '导入激活码失败' });
            }
        });

        // 导出激活码
        router.get('/keywords/:id/codes/export', async (req, res) => {
            try {
                const { id } = req.params;
                const { includeUsed = false } = req.query;
                
                const codes = await this.activationCodeManager.exportActivationCodes(
                    parseInt(id), 
                    includeUsed === 'true'
                );
                
                res.json({ success: true, data: codes });
            } catch (error) {
                Logger.error('导出激活码失败:', error);
                res.status(500).json({ success: false, message: '导出激活码失败' });
            }
        });

        // 重置激活码
        router.post('/keywords/:id/codes/reset', async (req, res) => {
            try {
                const { id } = req.params;
                const resetCount = await this.activationCodeManager.resetActivationCodes(parseInt(id));
                res.json({ success: true, data: { resetCount } });
            } catch (error) {
                Logger.error('重置激活码失败:', error);
                res.status(500).json({ success: false, message: '重置激活码失败' });
            }
        });

        // 获取激活码统计
        router.get('/keywords/:id/stats', async (req, res) => {
            try {
                const { id } = req.params;
                const stats = await this.activationCodeManager.getActivationCodeStats(parseInt(id));
                res.json({ success: true, data: stats });
            } catch (error) {
                Logger.error('获取统计信息失败:', error);
                res.status(500).json({ success: false, message: '获取统计信息失败' });
            }
        });

        // 删除激活码
        router.delete('/codes/:id', async (req, res) => {
            try {
                const { id } = req.params;
                await this.activationCodeManager.removeActivationCode(parseInt(id));
                res.json({ success: true, message: '激活码删除成功' });
            } catch (error) {
                Logger.error('删除激活码失败:', error);
                res.status(500).json({ success: false, message: '删除激活码失败' });
            }
        });

        // 获取用户使用历史
        router.get('/users/:userId/history', async (req, res) => {
            try {
                const { userId } = req.params;
                const { limit = 50 } = req.query;
                
                const history = await this.db.getUserUsageHistory(userId, parseInt(limit));
                res.json({ success: true, data: history });
            } catch (error) {
                Logger.error('获取用户历史失败:', error);
                res.status(500).json({ success: false, message: '获取用户历史失败' });
            }
        });

        // 系统统计信息
        router.get('/stats', async (req, res) => {
            try {
                const keywords = await this.db.getAllKeywords();
                const stats = {
                    totalKeywords: keywords.length,
                    keywordStats: []
                };

                for (const keyword of keywords) {
                    const keywordStats = await this.activationCodeManager.getActivationCodeStats(keyword.id);
                    stats.keywordStats.push({
                        keyword: keyword.keyword,
                        ...keywordStats
                    });
                }

                res.json({ success: true, data: stats });
            } catch (error) {
                Logger.error('获取系统统计失败:', error);
                res.status(500).json({ success: false, message: '获取系统统计失败' });
            }
        });
    }

    getRouter() {
        return router;
    }
}

module.exports = AdminRoutes;
