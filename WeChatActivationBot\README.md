# 微信公众号激活码自动回复系统

这是一个基于Node.js的微信公众号自动回复系统，用户发送特定关键字后，系统会自动回复对应的激活码。

## 功能特性

- 🔐 自动激活码分配和管理
- 🔍 智能关键字匹配
- 📊 使用统计和日志记录
- 🗄️ SQLite数据库存储
- ⚡ 高性能异步处理
- 🛡️ 微信消息验证和安全

## 项目结构

```
WeChatActivationBot/
├── src/
│   ├── app.js                 # 应用入口
│   ├── controllers/           # 控制器
│   │   ├── wechatController.js
│   │   └── activationController.js
│   ├── services/              # 业务逻辑
│   │   ├── wechatService.js
│   │   ├── keywordService.js
│   │   └── activationService.js
│   ├── models/                # 数据模型
│   │   ├── database.js
│   │   ├── Keyword.js
│   │   ├── ActivationCode.js
│   │   └── UsageLog.js
│   ├── utils/                 # 工具函数
│   │   ├── crypto.js
│   │   ├── logger.js
│   │   └── validator.js
│   └── config/                # 配置文件
│       └── config.js
├── scripts/                   # 脚本文件
│   ├── init-database.js
│   └── import-codes.js
├── tests/                     # 测试文件
├── data/                      # 数据文件
├── logs/                      # 日志文件
├── .env.example              # 环境变量示例
├── package.json
└── README.md
```

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 配置环境变量

```bash
cp .env.example .env
# 编辑 .env 文件，填入你的微信公众号配置
```

### 3. 初始化数据库

```bash
npm run init-db
```

### 4. 启动服务

```bash
# 开发模式
npm run dev

# 生产模式
npm start
```

## 配置说明

### 微信公众号配置

1. 登录微信公众平台
2. 在"开发"-"基本配置"中设置服务器配置
3. URL填写: `http://your-domain.com/wechat`
4. Token填写你在.env中设置的WECHAT_TOKEN

### 关键字和激活码管理

系统支持通过API或直接数据库操作来管理关键字和激活码：

- 添加关键字: POST /api/keywords
- 添加激活码: POST /api/activation-codes
- 查看统计: GET /api/stats

## API文档

详细的API文档请参考 `/docs/api.md`

## 部署指南

详细的部署指南请参考 `/docs/deployment.md`
