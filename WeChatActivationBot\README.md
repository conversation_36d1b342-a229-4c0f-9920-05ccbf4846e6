# 微信公众号激活码自动回复机器人

一个基于Node.js的微信公众号自动回复系统，支持关键词触发激活码发放，具有完整的管理后台和用户管理功能。

## ✨ 主要功能

- 🤖 **自动回复**: 用户发送关键词"emo"自动回复1天VIP激活码
- 🔐 **防重复机制**: 每个用户只能获取一次激活码
- 👥 **用户管理**: 完整的用户关注状态和激活码使用记录
- 🎛️ **管理后台**: Web界面管理关键词和查看统计信息
- 🗄️ **双数据库**: 支持外部激活码数据库集成
- 📊 **实时统计**: 用户数量、激活码使用情况等统计信息
- 🔒 **安全认证**: JWT令牌认证，密码哈希存储

## 🏗️ 技术架构

- 🖥️ **后端服务器**: Express.js + Node.js
- 🗄️ **数据库**: MySQL (双数据库架构)
- 🔌 **API接口**: RESTful API
- 🌐 **管理界面**: 响应式Web界面
- 🤖 **微信集成**: 公众号消息处理
- 🔐 **认证系统**: JWT + bcrypt

## 项目结构

```
WeChatActivationBot/
├── src/
│   ├── app.js                 # 应用入口
│   ├── controllers/           # 控制器
│   │   ├── wechatController.js
│   │   └── activationController.js
│   ├── services/              # 业务逻辑
│   │   ├── wechatService.js
│   │   ├── keywordService.js
│   │   └── activationService.js
│   ├── models/                # 数据模型
│   │   ├── database.js
│   │   ├── Keyword.js
│   │   ├── ActivationCode.js
│   │   └── UsageLog.js
│   ├── utils/                 # 工具函数
│   │   ├── crypto.js
│   │   ├── logger.js
│   │   └── validator.js
│   └── config/                # 配置文件
│       └── config.js
├── scripts/                   # 脚本文件
│   ├── init-database.js
│   └── import-codes.js
├── tests/                     # 测试文件
├── data/                      # 数据文件
├── logs/                      # 日志文件
├── .env.example              # 环境变量示例
├── package.json
└── README.md
```

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 配置环境变量

```bash
cp .env.example .env
# 编辑 .env 文件，填入你的微信公众号配置
```

### 3. 初始化数据库

```bash
npm run init-db
```

### 4. 启动服务

```bash
# 开发模式
npm run dev

# 生产模式
npm start
```

## 配置说明

### 微信公众号配置

1. 登录微信公众平台
2. 在"开发"-"基本配置"中设置服务器配置
3. URL填写: `http://your-domain.com/wechat`
4. Token填写你在.env中设置的WECHAT_TOKEN

### 关键字和激活码管理

系统支持通过API或直接数据库操作来管理关键字和激活码：

- 添加关键字: POST /api/keywords
- 添加激活码: POST /api/activation-codes
- 查看统计: GET /api/stats

## 本地开发

### 快速开始本地测试

```bash
# 1. 安装依赖
npm install

# 2. 初始化数据库
npm run init-db init

# 3. 运行本地测试
npm run local-test

# 4. 交互式测试
npm run local-test-interactive

# 5. 启动本地服务
npm run dev

# 6. 访问管理界面（可选）
# 浏览器打开 http://localhost:3000/admin.html
```

详细的本地开发指南请参考 `LOCAL_DEVELOPMENT.md`

## API接口

系统提供完整的REST API：

- `GET /admin/keywords` - 获取关键字列表
- `POST /admin/keywords` - 添加关键字
- `GET /admin/keywords/:id/codes` - 获取激活码
- `POST /admin/keywords/:id/codes/generate` - 生成激活码
- `GET /admin/stats` - 系统统计

## 部署指南

当本地开发完成后，可以部署到服务器。推荐使用：

1. **传统部署**: 直接在服务器运行 `npm start`
2. **PM2部署**: 使用 PM2 进行进程管理
3. **云服务**: 部署到阿里云、腾讯云等
