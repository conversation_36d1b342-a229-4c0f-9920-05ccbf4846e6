const express = require('express');
const crypto = require('crypto');
const xml2js = require('xml2js');
require('dotenv').config();

const WeChatService = require('./services/wechatService');
const KeywordMatcher = require('./services/keywordMatcher');
const ActivationCodeManager = require('./services/activationCodeManager');
const DatabaseService = require('./services/databaseService');
const AdminRoutes = require('./routes/admin');
const AuthMiddleware = require('./middleware/auth');
const Logger = require('./utils/logger');

class WeChatActivationBot {
    constructor() {
        this.app = express();
        this.port = process.env.PORT || 3000;
        this.token = process.env.WECHAT_TOKEN;
    }

    async init() {
        await this.initializeServices();
        this.setupMiddleware();
        this.setupRoutes();
    }

    async initializeServices() {
        try {
            // 初始化数据库
            this.db = new DatabaseService();
            await this.db.initialize();
            
            // 初始化服务
            this.wechatService = new WeChatService(this.token);
            this.keywordMatcher = new KeywordMatcher(this.db);
            this.activationCodeManager = new ActivationCodeManager(this.db);

            // 初始化认证中间件
            this.authMiddleware = new AuthMiddleware();

            // 初始化管理路由
            this.adminRoutes = new AdminRoutes(this.db, this.keywordMatcher, this.activationCodeManager);

            Logger.info('所有服务初始化完成');
        } catch (error) {
            Logger.error('服务初始化失败:', error);
            process.exit(1);
        }
    }

    setupMiddleware() {
        // 解析JSON和URL编码的请求体
        this.app.use(express.json());
        this.app.use(express.urlencoded({ extended: true }));

        // Cookie解析
        const cookieParser = require('cookie-parser');
        this.app.use(cookieParser());

        // 静态文件服务
        this.app.use(express.static('public'));

        // 解析原始请求体（仅用于微信接口）
        this.app.use('/wechat', express.raw({ type: 'text/xml' }));
    }

    setupRoutes() {
        // 微信验证接口
        this.app.get('/wechat', (req, res) => {
            const { signature, timestamp, nonce, echostr } = req.query;
            
            if (this.wechatService.verifySignature(signature, timestamp, nonce)) {
                Logger.info('微信验证成功');
                res.send(echostr);
            } else {
                Logger.warn('微信验证失败');
                res.status(403).send('验证失败');
            }
        });

        // 微信消息接收接口
        this.app.post('/wechat', async (req, res) => {
            try {
                const xmlData = req.body.toString();
                const message = await this.wechatService.parseMessage(xmlData);
                
                Logger.info('收到消息:', message);
                
                const response = await this.handleMessage(message);
                res.set('Content-Type', 'text/xml');
                res.send(response);
            } catch (error) {
                Logger.error('处理消息失败:', error);
                res.status(500).send('服务器错误');
            }
        });

        // 认证接口（不需要认证）
        this.app.post('/admin/auth/login', (req, res) => this.authMiddleware.handleLogin(req, res));
        this.app.post('/admin/auth/logout', (req, res) => this.authMiddleware.handleLogout(req, res));
        this.app.get('/admin/auth/status', this.authMiddleware.optionalAuth(), (req, res) => this.authMiddleware.checkAuthStatus(req, res));

        // 管理接口（需要认证）
        this.app.use('/admin', this.authMiddleware.requireAuth(), this.adminRoutes.getRouter());

        // 健康检查接口
        this.app.get('/health', (req, res) => {
            res.json({ status: 'ok', timestamp: new Date().toISOString() });
        });
    }

    async handleMessage(message) {
        const userId = message.FromUserName;

        // 处理关注/取消关注事件
        if (message.MsgType === 'event') {
            if (message.Event === 'subscribe') {
                // 用户关注事件
                await this.db.handleUserSubscribe(userId);
                return this.wechatService.createTextResponse(
                    userId,
                    message.ToUserName,
                    '🎉 欢迎关注！\n\n发送 "emo" 获取1天VIP激活码~'
                );
            } else if (message.Event === 'unsubscribe') {
                // 用户取消关注事件
                await this.db.handleUserUnsubscribe(userId);
                return '';
            }
        }

        // 处理文本消息
        if (message.MsgType !== 'text') {
            return this.wechatService.createTextResponse(
                userId,
                message.ToUserName,
                '请发送文字消息哦~ 发送 "emo" 获取激活码！'
            );
        }

        const content = message.Content.trim().toLowerCase();

        try {
            // 处理关键词回复
            const result = await this.db.handleKeywordReply(userId, content);

            return this.wechatService.createTextResponse(
                userId,
                message.ToUserName,
                result.message
            );
        } catch (error) {
            Logger.error('处理消息失败:', error);
            return this.wechatService.createTextResponse(
                userId,
                message.ToUserName,
                '系统错误，请稍后重试~'
            );
        }
    }

    start() {
        this.app.listen(this.port, () => {
            Logger.info(`微信激活码机器人启动成功，端口: ${this.port}`);
        });
    }
}

// 启动应用
async function startBot() {
    const bot = new WeChatActivationBot();
    await bot.init();
    bot.start();
}

startBot().catch(error => {
    Logger.error('启动失败:', error);
    process.exit(1);
});

module.exports = WeChatActivationBot;
