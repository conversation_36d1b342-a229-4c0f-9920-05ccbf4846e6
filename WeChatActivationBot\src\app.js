const express = require('express');
const crypto = require('crypto');
const xml2js = require('xml2js');
require('dotenv').config();

const WeChatService = require('./services/wechatService');
const KeywordMatcher = require('./services/keywordMatcher');
const ActivationCodeManager = require('./services/activationCodeManager');
const DatabaseService = require('./services/databaseService');
const AdminRoutes = require('./routes/admin');
const Logger = require('./utils/logger');

class WeChatActivationBot {
    constructor() {
        this.app = express();
        this.port = process.env.PORT || 3000;
        this.token = process.env.WECHAT_TOKEN;
        
        this.initializeServices();
        this.setupMiddleware();
        this.setupRoutes();
    }

    async initializeServices() {
        try {
            // 初始化数据库
            this.db = new DatabaseService();
            await this.db.initialize();
            
            // 初始化服务
            this.wechatService = new WeChatService(this.token);
            this.keywordMatcher = new KeywordMatcher(this.db);
            this.activationCodeManager = new ActivationCodeManager(this.db);

            // 初始化管理路由
            this.adminRoutes = new AdminRoutes(this.db, this.keywordMatcher, this.activationCodeManager);

            Logger.info('所有服务初始化完成');
        } catch (error) {
            Logger.error('服务初始化失败:', error);
            process.exit(1);
        }
    }

    setupMiddleware() {
        // 静态文件服务
        this.app.use(express.static('public'));

        // 解析原始请求体
        this.app.use('/wechat', express.raw({ type: 'text/xml' }));
        this.app.use(express.json());
    }

    setupRoutes() {
        // 微信验证接口
        this.app.get('/wechat', (req, res) => {
            const { signature, timestamp, nonce, echostr } = req.query;
            
            if (this.wechatService.verifySignature(signature, timestamp, nonce)) {
                Logger.info('微信验证成功');
                res.send(echostr);
            } else {
                Logger.warn('微信验证失败');
                res.status(403).send('验证失败');
            }
        });

        // 微信消息接收接口
        this.app.post('/wechat', async (req, res) => {
            try {
                const xmlData = req.body.toString();
                const message = await this.wechatService.parseMessage(xmlData);
                
                Logger.info('收到消息:', message);
                
                const response = await this.handleMessage(message);
                res.set('Content-Type', 'text/xml');
                res.send(response);
            } catch (error) {
                Logger.error('处理消息失败:', error);
                res.status(500).send('服务器错误');
            }
        });

        // 管理接口
        this.app.use('/admin', this.adminRoutes.getRouter());

        // 健康检查接口
        this.app.get('/health', (req, res) => {
            res.json({ status: 'ok', timestamp: new Date().toISOString() });
        });
    }

    async handleMessage(message) {
        if (message.MsgType !== 'text') {
            return this.wechatService.createTextResponse(
                message.FromUserName,
                message.ToUserName,
                '抱歉，我只能处理文本消息。请发送关键字获取激活码。'
            );
        }

        const keyword = message.Content.trim();
        const userId = message.FromUserName;

        try {
            // 匹配关键字
            const matchedKeyword = await this.keywordMatcher.findMatch(keyword);
            
            if (!matchedKeyword) {
                return this.wechatService.createTextResponse(
                    userId,
                    message.ToUserName,
                    '未找到匹配的关键字。请检查您的输入或联系管理员。'
                );
            }

            // 获取激活码
            const activationCode = await this.activationCodeManager.getActivationCode(
                matchedKeyword.id,
                userId
            );

            if (!activationCode) {
                return this.wechatService.createTextResponse(
                    userId,
                    message.ToUserName,
                    '抱歉，该关键字的激活码已用完。请联系管理员。'
                );
            }

            const responseText = `您的激活码是：${activationCode.code}\n\n请妥善保管，激活码仅可使用一次。`;
            
            return this.wechatService.createTextResponse(
                userId,
                message.ToUserName,
                responseText
            );

        } catch (error) {
            Logger.error('处理关键字失败:', error);
            return this.wechatService.createTextResponse(
                userId,
                message.ToUserName,
                '系统繁忙，请稍后再试。'
            );
        }
    }

    start() {
        this.app.listen(this.port, () => {
            Logger.info(`微信激活码机器人启动成功，端口: ${this.port}`);
        });
    }
}

// 启动应用
const bot = new WeChatActivationBot();
bot.start();

module.exports = WeChatActivationBot;
