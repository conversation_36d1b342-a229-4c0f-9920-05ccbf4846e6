package com.emotional.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.emotional.service.entity.ActivationCode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 激活码Mapper接口
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Mapper
public interface ActivationCodeMapper extends BaseMapper<ActivationCode> {
    
    /**
     * 检查激活码是否存在
     * 
     * @param code 激活码
     * @return 是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM activation_codes WHERE code = #{code}")
    boolean existsByCode(@Param("code") String code);
    
    /**
     * 根据激活码查询（不限状态）
     *
     * @param code 激活码
     * @return 激活码信息
     */
    @Select("SELECT * FROM activation_codes WHERE code = #{code}")
    ActivationCode findByCode(@Param("code") String code);

    /**
     * 根据激活码查询未使用的
     *
     * @param code 激活码
     * @return 激活码信息
     */
    @Select("SELECT * FROM activation_codes WHERE code = #{code} AND status = 0")
    ActivationCode findByCodeAndUnused(@Param("code") String code);
}
