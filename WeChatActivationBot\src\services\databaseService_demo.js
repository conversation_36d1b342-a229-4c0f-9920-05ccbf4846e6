// 简化版数据库服务 - 使用内存存储，便于演示
const Logger = require('../utils/logger');

class DatabaseService {
    constructor() {
        // 使用内存存储进行演示
        this.keywords = [];
        this.activationCodes = [];
        this.nextKeywordId = 1;
        this.nextCodeId = 1;
    }

    async initialize() {
        try {
            // 创建示例数据
            this.createSampleData();
            Logger.info('内存数据库初始化完成（演示模式）');
        } catch (error) {
            Logger.error('数据库初始化失败:', error);
            throw error;
        }
    }

    createSampleData() {
        // 添加示例关键字
        const sampleKeywords = [
            { keyword: 'VIP会员', description: 'VIP会员激活码' },
            { keyword: '高级版', description: '软件高级版激活码' },
            { keyword: '专业版', description: '专业版功能激活码' },
            { keyword: '测试', description: '测试用激活码' }
        ];

        sampleKeywords.forEach(data => {
            const keyword = {
                id: this.nextKeywordId++,
                keyword: data.keyword,
                description: data.description,
                created_at: new Date().toISOString()
            };
            this.keywords.push(keyword);

            // 为每个关键字生成一些激活码
            for (let i = 0; i < 10; i++) {
                const code = {
                    id: this.nextCodeId++,
                    keyword_id: keyword.id,
                    code: this.generateRandomCode(16),
                    used_by: null,
                    used_at: null,
                    created_at: new Date().toISOString()
                };
                this.activationCodes.push(code);
            }
        });
    }

    generateRandomCode(length) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    // ==================== 关键字相关方法 ====================

    async getAllKeywords() {
        return [...this.keywords].sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
    }

    async getKeywordById(id) {
        return this.keywords.find(k => k.id === parseInt(id));
    }

    async addKeyword(keyword, description = '') {
        const newKeyword = {
            id: this.nextKeywordId++,
            keyword,
            description,
            created_at: new Date().toISOString()
        };
        this.keywords.push(newKeyword);
        return newKeyword;
    }

    async removeKeyword(id) {
        const index = this.keywords.findIndex(k => k.id === parseInt(id));
        if (index > -1) {
            this.keywords.splice(index, 1);
            // 同时删除相关的激活码
            this.activationCodes = this.activationCodes.filter(c => c.keyword_id !== parseInt(id));
        }
        return { affectedRows: index > -1 ? 1 : 0 };
    }

    // ==================== 激活码相关方法 ====================

    async getAvailableActivationCode(keywordId) {
        return this.activationCodes
            .filter(c => c.keyword_id === parseInt(keywordId) && !c.used_by)
            .sort((a, b) => new Date(a.created_at) - new Date(b.created_at))[0];
    }

    async assignActivationCode(codeId, userId) {
        const code = this.activationCodes.find(c => c.id === parseInt(codeId));
        if (code) {
            code.used_by = userId;
            code.used_at = new Date().toISOString();
        }
        return { affectedRows: code ? 1 : 0 };
    }

    async getUserActivationCode(keywordId, userId) {
        return this.activationCodes.find(c =>
            c.keyword_id === parseInt(keywordId) && c.used_by === userId
        );
    }

    async batchInsertActivationCodes(keywordId, codes) {
        const results = [];
        codes.forEach(code => {
            const newCode = {
                id: this.nextCodeId++,
                keyword_id: parseInt(keywordId),
                code,
                used_by: null,
                used_at: null,
                created_at: new Date().toISOString()
            };
            this.activationCodes.push(newCode);
            results.push(newCode);
        });
        return results;
    }

    async getActivationCodesByKeyword(keywordId) {
        return this.activationCodes
            .filter(c => c.keyword_id === parseInt(keywordId))
            .sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
    }

    async getActivationCodeByCode(code) {
        return this.activationCodes.find(c => c.code === code);
    }

    async addActivationCode(keywordId, code) {
        const newCode = {
            id: this.nextCodeId++,
            keyword_id: parseInt(keywordId),
            code,
            used_by: null,
            used_at: null,
            created_at: new Date().toISOString()
        };
        this.activationCodes.push(newCode);
        return newCode;
    }

    async removeActivationCode(id) {
        const index = this.activationCodes.findIndex(c => c.id === parseInt(id));
        if (index > -1) {
            this.activationCodes.splice(index, 1);
        }
        return { affectedRows: index > -1 ? 1 : 0 };
    }

    async getActivationCodeStats(keywordId) {
        const codes = this.activationCodes.filter(c => c.keyword_id === parseInt(keywordId));
        const total = codes.length;
        const used = codes.filter(c => c.used_by).length;
        return { total, used };
    }

    async resetActivationCodes(keywordId) {
        let count = 0;
        this.activationCodes.forEach(c => {
            if (c.keyword_id === parseInt(keywordId) && c.used_by) {
                c.used_by = null;
                c.used_at = null;
                count++;
            }
        });
        return count;
    }

    async close() {
        Logger.info('内存数据库连接已关闭');
    }

    async testConnection() {
        return true;
    }
}

module.exports = DatabaseService;