package com.emotional.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.emotional.service.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 用户 Mapper 接口
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {
    
    /**
     * 更新用户今日使用次数
     * 
     * @param userId 用户ID
     * @param increment 增量
     * @return 影响行数
     */
    @Update("UPDATE users SET today_used = today_used + #{increment}, " +
            "total_used = total_used + #{increment} WHERE id = #{userId}")
    int updateUsageCount(@Param("userId") Long userId, @Param("increment") int increment);
    
    /**
     * 重置所有用户的今日使用次数（定时任务使用）
     *
     * @return 影响行数
     */
    @Update("UPDATE users SET today_used = 0")
    int resetTodayUsage();

    /**
     * 更新用户VIP信息
     *
     * @param userId 用户ID
     * @param isVip VIP状态
     * @param vipExpireTime VIP过期时间
     * @param dailyQuota 每日配额
     * @return 影响行数
     */
    @Update("UPDATE users SET is_vip = #{isVip}, vip_expire_time = #{vipExpireTime}, " +
            "daily_quota = #{dailyQuota}, update_time = NOW() WHERE id = #{userId}")
    int updateUserVipInfo(@Param("userId") Long userId,
                         @Param("isVip") Integer isVip,
                         @Param("vipExpireTime") java.time.LocalDateTime vipExpireTime,
                         @Param("dailyQuota") Integer dailyQuota);

    /**
     * 批量降级过期VIP用户
     *
     * @return 影响行数
     */
    @Update("UPDATE users SET is_vip = 0, vip_expire_time = NULL, daily_quota = 3, update_time = NOW() " +
            "WHERE is_vip = 1 AND vip_expire_time IS NOT NULL AND vip_expire_time <= NOW() AND deleted = 0")
    int batchDowngradeExpiredVipUsers();

    /**
     * 分批获取过期VIP用户
     *
     * @param limit 每批数量
     * @param offset 偏移量
     * @return 过期VIP用户列表
     */
    @Select("SELECT id, username, nickname, is_vip, daily_quota, vip_expire_time, create_time, update_time " +
            "FROM users WHERE is_vip = 1 AND vip_expire_time IS NOT NULL AND vip_expire_time <= NOW() AND deleted = 0 " +
            "ORDER BY vip_expire_time ASC LIMIT #{limit} OFFSET #{offset}")
    List<User> getExpiredVipUsersBatch(@Param("limit") int limit, @Param("offset") int offset);
}
