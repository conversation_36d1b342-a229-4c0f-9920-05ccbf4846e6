/**
 * 安全的错误处理工具
 * 防止敏感信息泄露
 */

/**
 * 清理错误信息中的敏感数据
 * @param {string} message - 原始错误信息
 * @returns {string} 清理后的错误信息
 */
export const sanitizeErrorMessage = (message) => {
  if (!message || typeof message !== 'string') {
    return '操作失败，请稍后重试'
  }

  // 移除IP地址
  message = message.replace(/\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b/g, '[IP_HIDDEN]')
  
  // 移除端口号
  message = message.replace(/:(?:80|443|8080|8000|5000|3000|9000|5173)\b/g, ':[PORT]')
  
  // 移除localhost和127.0.0.1
  message = message.replace(/\blocalhost\b/gi, '[LOCAL]')
  message = message.replace(/\b127\.0\.0\.1\b/g, '[LOCAL]')
  
  // 移除完整的URL
  message = message.replace(/https?:\/\/[^\s/$.?#].[^\s]*/g, '[URL_HIDDEN]')
  
  // 移除文件路径
  message = message.replace(/[A-Za-z]:\\[^\\/:*?"<>|\r\n]+/g, '[PATH_HIDDEN]')
  message = message.replace(/\/[^/:*?"<>|\r\n]+/g, '[PATH_HIDDEN]')
  
  // 移除API密钥等敏感信息
  message = message.replace(/sk-[a-zA-Z0-9]{32,}/g, '[API_KEY_HIDDEN]')
  message = message.replace(/Bearer\s+[a-zA-Z0-9._-]+/g, 'Bearer [TOKEN_HIDDEN]')
  
  return message
}

/**
 * 获取用户友好的错误信息
 * @param {Error|string} error - 错误对象或错误信息
 * @returns {string} 用户友好的错误信息
 */
export const getUserFriendlyError = (error) => {
  let message = ''
  
  if (error && typeof error === 'object') {
    if (error.message) {
      message = error.message
    } else if (error.data && error.data.message) {
      message = error.data.message
    } else if (error.errMsg) {
      message = error.errMsg
    }
  } else if (typeof error === 'string') {
    message = error
  }
  
  // 清理敏感信息
  message = sanitizeErrorMessage(message)
  
  // 如果清理后的信息包含隐藏标记，返回通用错误信息
  if (message.includes('[') && message.includes('_HIDDEN]')) {
    return '网络连接失败，请检查网络后重试'
  }
  
  // 常见错误的友好提示
  const errorMappings = {
    'timeout': '请求超时，请检查网络连接',
    'network': '网络连接失败',
    'server': '服务器暂时不可用',
    'unauthorized': '登录已过期，请重新登录',
    'forbidden': '没有权限执行此操作',
    'not found': '请求的资源不存在',
    'internal server error': '服务器内部错误'
  }
  
  const lowerMessage = message.toLowerCase()
  for (const [key, friendlyMessage] of Object.entries(errorMappings)) {
    if (lowerMessage.includes(key)) {
      return friendlyMessage
    }
  }
  
  return message || '操作失败，请稍后重试'
}

/**
 * 安全的错误日志记录
 * @param {string} context - 错误上下文
 * @param {Error|string} error - 错误对象
 */
export const logError = (context, error) => {
  // 只在开发环境输出详细错误信息
  // #ifdef H5
  console.error(`[${context}]`, error)
  // #endif
  
  // 生产环境只记录基本信息
  // #ifdef APP-PLUS
  console.log(`[${context}] 发生错误`)
  // #endif
}

/**
 * 显示用户友好的错误提示
 * @param {Error|string} error - 错误对象
 * @param {string} defaultMessage - 默认错误信息
 */
export const showErrorToast = (error, defaultMessage = '操作失败，请稍后重试') => {
  const message = getUserFriendlyError(error) || defaultMessage
  
  uni.showToast({
    title: message,
    icon: 'none',
    duration: 3000
  })
}

/**
 * 显示详细的错误对话框（不包含敏感信息）
 * @param {Error|string} error - 错误对象
 * @param {string} title - 对话框标题
 */
export const showErrorModal = (error, title = '操作失败') => {
  const message = getUserFriendlyError(error)
  
  uni.showModal({
    title: title,
    content: message,
    showCancel: false,
    confirmText: '确定'
  })
}
