const mysql = require('mysql2/promise');
const Logger = require('../utils/logger');

class DatabaseService {
    constructor() {
        this.activationConnection = null;  // 外部激活码数据库连接
        this.botConnection = null;         // 机器人数据库连接

        this.baseConfig = {
            host: process.env.DB_HOST || 'localhost',
            port: process.env.DB_PORT || 3306,
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || '',
            charset: 'utf8mb4'
        };

        this.activationDbName = process.env.ACTIVATION_DB_NAME || 'emotional_reply_db';
        this.botDbName = process.env.BOT_DB_NAME || 'wechat_reply_bot';
    }

    /**
     * 初始化数据库连接
     */
    async initialize() {
        try {
            Logger.info('开始初始化数据库连接...');

            // 连接到激活码数据库
            this.activationConnection = await mysql.createConnection({
                ...this.baseConfig,
                database: this.activationDbName
            });
            Logger.info(`✅ 已连接到激活码数据库: ${this.activationDbName}`);

            // 连接到机器人数据库
            if (this.botDbName !== this.activationDbName) {
                // 先检查机器人数据库是否存在，不存在则创建
                const tempConnection = await mysql.createConnection(this.baseConfig);
                await tempConnection.execute(`CREATE DATABASE IF NOT EXISTS \`${this.botDbName}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
                await tempConnection.end();

                this.botConnection = await mysql.createConnection({
                    ...this.baseConfig,
                    database: this.botDbName
                });
                Logger.info(`✅ 已连接到机器人数据库: ${this.botDbName}`);
            } else {
                // 如果是同一个数据库，使用相同的连接
                this.botConnection = this.activationConnection;
                Logger.info('✅ 激活码和机器人使用同一个数据库');
            }

            // 确保机器人表存在
            await this.ensureBotTablesExist();
            
            // 检查激活码表结构
            await this.checkActivationCodeTable();
            
        } catch (error) {
            Logger.error('数据库初始化失败:', error);
            throw error;
        }
    }

    /**
     * 检查机器人数据库表是否存在
     */
    async ensureBotTablesExist() {
        try {
            // 简单检查表是否存在，如果不存在提示用户运行SQL初始化脚本
            const [tables] = await this.botConnection.execute(`
                SHOW TABLES LIKE 'wechat_users'
            `);

            if (tables.length === 0) {
                Logger.warn('⚠️  机器人数据库表不存在！');
                Logger.warn('请先运行数据库初始化脚本：');
                Logger.warn('mysql -u root -p wechat_reply_bot < database/init.sql');
                Logger.warn('或者使用: npm run init-db');
                throw new Error('数据库表未初始化');
            }

            Logger.info('✅ 机器人数据库表检查完成');
        } catch (error) {
            Logger.error('检查机器人表失败:', error);
            throw error;
        }
    }



    /**
     * 检查激活码表结构
     */
    async checkActivationCodeTable() {
        try {
            const [columns] = await this.activationConnection.execute(`
                SELECT COLUMN_NAME 
                FROM information_schema.COLUMNS 
                WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'activation_codes'
            `, [this.activationDbName]);
            
            const columnNames = columns.map(col => col.COLUMN_NAME);
            Logger.info('激活码表字段:', columnNames);
            
            // 检查总数
            const [count] = await this.activationConnection.execute('SELECT COUNT(*) as total FROM activation_codes');
            Logger.info(`激活码表中共有 ${count[0].total} 条记录`);
            
        } catch (error) {
            Logger.error('检查激活码表失败:', error);
        }
    }

    // ==================== 微信用户相关方法 ====================

    /**
     * 用户关注事件
     */
    async handleUserSubscribe(openid, nickname = null) {
        try {
            await this.botConnection.execute(`
                INSERT INTO wechat_users (openid, nickname, is_subscribed, subscribe_time)
                VALUES (?, ?, 1, NOW())
                ON DUPLICATE KEY UPDATE
                is_subscribed = 1,
                subscribe_time = NOW(),
                nickname = COALESCE(?, nickname)
            `, [openid, nickname, nickname]);

            Logger.info(`用户关注: ${openid}`);
            return true;
        } catch (error) {
            Logger.error('处理用户关注失败:', error);
            return false;
        }
    }

    /**
     * 用户取消关注事件
     */
    async handleUserUnsubscribe(openid) {
        try {
            await this.botConnection.execute(`
                UPDATE wechat_users
                SET is_subscribed = 0, unsubscribe_time = NOW()
                WHERE openid = ?
            `, [openid]);

            Logger.info(`用户取消关注: ${openid}`);
            return true;
        } catch (error) {
            Logger.error('处理用户取消关注失败:', error);
            return false;
        }
    }

    /**
     * 检查用户是否已经获取过激活码
     */
    async hasUserUsedActivationCode(openid) {
        try {
            const [rows] = await this.botConnection.execute(`
                SELECT activation_code_used
                FROM wechat_users
                WHERE openid = ? AND activation_code_used IS NOT NULL
            `, [openid]);

            return rows.length > 0;
        } catch (error) {
            Logger.error('检查用户激活码使用状态失败:', error);
            return true; // 出错时返回true，避免重复发放
        }
    }

    async getKeywordById(id) {
        try {
            const [rows] = await this.keywordConnection.execute('SELECT * FROM keywords WHERE id = ?', [id]);
            const keyword = rows[0];
            if (keyword) {
                keyword.activation_code_stats = await this.getActivationCodeStats(id);
            }
            return keyword;
        } catch (error) {
            Logger.error('获取关键字失败:', error);
            return null;
        }
    }

    async addKeyword(keyword, description = '') {
        try {
            const [result] = await this.keywordConnection.execute(
                'INSERT INTO keywords (keyword, description) VALUES (?, ?)', 
                [keyword, description]
            );
            return await this.getKeywordById(result.insertId);
        } catch (error) {
            Logger.error('添加关键字失败:', error);
            throw error;
        }
    }

    async removeKeyword(id) {
        try {
            const [result] = await this.keywordConnection.execute('DELETE FROM keywords WHERE id = ?', [id]);
            return result;
        } catch (error) {
            Logger.error('删除关键字失败:', error);
            throw error;
        }
    }

    // ==================== 激活码相关方法 ====================

    /**
     * 获取指定类型的可用激活码
     */
    async getAvailableActivationCode(codeType = 'vip_1d') {
        try {
            const [rows] = await this.activationConnection.execute(`
                SELECT * FROM activation_codes
                WHERE code_type = ? AND status = 0 AND used_by IS NULL
                ORDER BY created_time ASC
                LIMIT 1
            `, [codeType]);

            return rows[0];
        } catch (error) {
            Logger.error('获取可用激活码失败:', error);
            return null;
        }
    }

    /**
     * 标记激活码为已使用
     */
    async markActivationCodeAsUsed(codeId, openid) {
        try {
            // 更新外部激活码数据库
            await this.activationConnection.execute(`
                UPDATE activation_codes
                SET used_by = ?, used_time = NOW(), status = 1
                WHERE id = ?
            `, [openid, codeId]);

            return true;
        } catch (error) {
            Logger.error('标记激活码为已使用失败:', error);
            return false;
        }
    }

    /**
     * 为用户分配激活码
     */
    async assignActivationCodeToUser(openid, codeType = 'vip_1d') {
        try {
            // 1. 检查用户是否已经获取过激活码
            const hasUsed = await this.hasUserUsedActivationCode(openid);
            if (hasUsed) {
                return { success: false, message: '您已经获取过激活码了哦~' };
            }

            // 2. 获取可用的激活码
            const activationCode = await this.getAvailableActivationCode(codeType);
            if (!activationCode) {
                return { success: false, message: '抱歉，激活码已发完，请联系管理员~' };
            }

            // 3. 标记激活码为已使用
            const marked = await this.markActivationCodeAsUsed(activationCode.id, openid);
            if (!marked) {
                return { success: false, message: '系统错误，请稍后重试~' };
            }

            // 4. 更新用户记录
            await this.botConnection.execute(`
                UPDATE wechat_users
                SET activation_code_used = ?, activation_code_time = NOW()
                WHERE openid = ?
            `, [activationCode.code, openid]);

            Logger.info(`为用户 ${openid} 分配激活码: ${activationCode.code}`);

            return {
                success: true,
                code: activationCode.code,
                message: '激活码获取成功！'
            };

        } catch (error) {
            Logger.error('分配激活码失败:', error);
            return { success: false, message: '系统错误，请稍后重试~' };
        }
    }

    // ==================== 关键词管理方法 ====================

    /**
     * 获取关键词配置
     */
    async getKeywordConfig(keyword) {
        try {
            const [rows] = await this.botConnection.execute(`
                SELECT * FROM reply_keywords
                WHERE keyword = ? AND is_active = 1
            `, [keyword]);

            return rows[0];
        } catch (error) {
            Logger.error('获取关键词配置失败:', error);
            return null;
        }
    }

    /**
     * 处理关键词回复
     */
    async handleKeywordReply(openid, keyword) {
        try {
            const config = await this.getKeywordConfig(keyword);
            if (!config) {
                return { success: false, message: '未识别的关键词' };
            }

            if (config.reply_type === 'activation_code') {
                // 激活码类型回复
                const result = await this.assignActivationCodeToUser(openid, config.activation_code_type);
                if (result.success) {
                    // 替换模板中的激活码
                    const replyText = config.reply_content.replace('{code}', result.code);
                    return { success: true, message: replyText };
                } else {
                    return result;
                }
            } else {
                // 普通文本回复
                return { success: true, message: config.reply_content };
            }
        } catch (error) {
            Logger.error('处理关键词回复失败:', error);
            return { success: false, message: '系统错误，请稍后重试~' };
        }
    }

    /**
     * 获取所有关键词配置
     */
    async getAllKeywords() {
        try {
            const [rows] = await this.botConnection.execute(`
                SELECT * FROM reply_keywords
                ORDER BY created_at DESC
            `);
            return rows;
        } catch (error) {
            Logger.error('获取关键词列表失败:', error);
            return [];
        }
    }

    /**
     * 添加关键词
     */
    async addKeyword(keyword, replyType, replyContent, activationCodeType = null) {
        try {
            const [result] = await this.botConnection.execute(`
                INSERT INTO reply_keywords (keyword, reply_type, reply_content, activation_code_type)
                VALUES (?, ?, ?, ?)
            `, [keyword, replyType, replyContent, activationCodeType]);

            return { success: true, id: result.insertId };
        } catch (error) {
            Logger.error('添加关键词失败:', error);
            return { success: false, message: '添加关键词失败' };
        }
    }

    async getUserActivationCode(keywordId, userId) {
        try {
            const [rows] = await this.activationConnection.execute(`
                SELECT * FROM activation_codes 
                WHERE used_by = ?
            `, [userId]);
            return rows[0];
        } catch (error) {
            Logger.error('获取用户激活码失败:', error);
            return null;
        }
    }

    // 注意：激活码由外部系统管理，这里不提供插入方法

    async getActivationCodesByKeyword(keywordId) {
        try {
            // 返回所有激活码（因为激活码表中没有keyword_id字段）
            const [rows] = await this.activationConnection.execute(
                'SELECT * FROM activation_codes ORDER BY id DESC'
            );
            return rows;
        } catch (error) {
            Logger.error('获取激活码失败:', error);
            return [];
        }
    }

    async getAllActivationCodes() {
        try {
            const [rows] = await this.activationConnection.execute(
                'SELECT * FROM activation_codes ORDER BY id DESC'
            );
            return rows;
        } catch (error) {
            Logger.error('获取所有激活码失败:', error);
            return [];
        }
    }

    async getActivationCodeByCode(code) {
        try {
            const [rows] = await this.activationConnection.execute('SELECT * FROM activation_codes WHERE code = ?', [code]);
            return rows[0];
        } catch (error) {
            Logger.error('根据代码获取激活码失败:', error);
            return null;
        }
    }

    async addActivationCode(keywordId, code) {
        try {
            const [result] = await this.activationConnection.execute(
                'INSERT INTO activation_codes (code) VALUES (?)', 
                [code]
            );
            return { id: result.insertId, code };
        } catch (error) {
            Logger.error('添加激活码失败:', error);
            throw error;
        }
    }

    async removeActivationCode(id) {
        try {
            const [result] = await this.activationConnection.execute('DELETE FROM activation_codes WHERE id = ?', [id]);
            return result;
        } catch (error) {
            Logger.error('删除激活码失败:', error);
            throw error;
        }
    }

    async getActivationCodeStats(keywordId) {
        try {
            const [rows] = await this.activationConnection.execute(`
                SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN used_by IS NOT NULL AND used_by != '' THEN 1 END) as used
                FROM activation_codes
            `);
            return rows[0];
        } catch (error) {
            Logger.error('获取激活码统计失败:', error);
            return { total: 0, used: 0 };
        }
    }

    async resetActivationCodes(keywordId) {
        try {
            const [result] = await this.activationConnection.execute(
                'UPDATE activation_codes SET used_by = NULL, used_at = NULL'
            );
            return result.affectedRows;
        } catch (error) {
            Logger.error('重置激活码失败:', error);
            throw error;
        }
    }

    /**
     * 关闭数据库连接
     */
    async close() {
        try {
            if (this.activationConnection) {
                await this.activationConnection.end();
                Logger.info('✅ 激活码数据库连接已关闭');
            }
            if (this.botConnection && this.botConnection !== this.activationConnection) {
                await this.botConnection.end();
                Logger.info('✅ 机器人数据库连接已关闭');
            }
        } catch (error) {
            Logger.error('关闭数据库连接失败:', error);
        }
    }

    /**
     * 测试数据库连接
     */
    async testConnection() {
        try {
            if (this.activationConnection) {
                await this.activationConnection.ping();
            }
            if (this.botConnection && this.botConnection !== this.activationConnection) {
                await this.botConnection.ping();
            }
            Logger.info('✅ 数据库连接测试成功');
            return true;
        } catch (error) {
            Logger.error('❌ 数据库连接测试失败:', error);
            return false;
        }
    }
}

module.exports = DatabaseService;
