#!/usr/bin/env node

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const DatabaseService = require('../src/services/databaseService');
const ActivationCodeManager = require('../src/services/activationCodeManager');
const Logger = require('../src/utils/logger');

class DatabaseInitializer {
    constructor() {
        this.db = new DatabaseService();
        this.activationCodeManager = null;
    }

    async initialize() {
        try {
            Logger.info('开始初始化数据库...');
            
            // 初始化数据库
            await this.db.initialize();
            this.activationCodeManager = new ActivationCodeManager(this.db);
            
            // 创建示例数据
            await this.createSampleData();
            
            Logger.info('数据库初始化完成！');
            
        } catch (error) {
            Logger.error('数据库初始化失败:', error);
            process.exit(1);
        } finally {
            this.db.close();
        }
    }

    async createSampleData() {
        Logger.info('创建示例数据...');
        
        // 示例关键字
        const sampleKeywords = [
            { keyword: 'VIP会员', description: 'VIP会员激活码' },
            { keyword: '高级版', description: '软件高级版激活码' },
            { keyword: '专业版', description: '专业版功能激活码' },
            { keyword: '测试', description: '测试用激活码' }
        ];

        for (const keywordData of sampleKeywords) {
            try {
                // 检查关键字是否已存在
                const existing = await this.db.getAllKeywords();
                const exists = existing.some(k => k.keyword === keywordData.keyword);
                
                if (!exists) {
                    const keyword = await this.db.addKeyword(keywordData.keyword, keywordData.description);
                    Logger.info(`创建关键字: ${keyword.keyword}`);
                    
                    // 为每个关键字生成一些激活码
                    await this.activationCodeManager.generateActivationCodes(keyword.id, 10);
                    Logger.info(`为关键字 "${keyword.keyword}" 生成了 10 个激活码`);
                } else {
                    Logger.info(`关键字 "${keywordData.keyword}" 已存在，跳过创建`);
                }
            } catch (error) {
                Logger.error(`创建关键字 "${keywordData.keyword}" 失败:`, error);
            }
        }
    }

    async showStats() {
        try {
            const keywords = await this.db.getAllKeywords();
            
            console.log('\n=== 数据库统计信息 ===');
            console.log(`关键字总数: ${keywords.length}`);
            
            for (const keyword of keywords) {
                const stats = await this.activationCodeManager.getActivationCodeStats(keyword.id);
                console.log(`关键字 "${keyword.keyword}": 总计 ${stats.total} 个激活码，已使用 ${stats.used} 个，可用 ${stats.available} 个`);
            }
            
            console.log('========================\n');
            
        } catch (error) {
            Logger.error('获取统计信息失败:', error);
        }
    }
}

// 命令行参数处理
const args = process.argv.slice(2);
const command = args[0];

async function main() {
    const initializer = new DatabaseInitializer();
    
    switch (command) {
        case 'init':
            await initializer.initialize();
            break;
        case 'stats':
            await initializer.db.initialize();
            initializer.activationCodeManager = new ActivationCodeManager(initializer.db);
            await initializer.showStats();
            initializer.db.close();
            break;
        case 'reset':
            Logger.warn('重置数据库...');
            const fs = require('fs');
            const dbPath = process.env.DB_PATH || './data/activation_bot.db';
            if (fs.existsSync(dbPath)) {
                fs.unlinkSync(dbPath);
                Logger.info('数据库文件已删除');
            }
            await initializer.initialize();
            break;
        default:
            console.log(`
使用方法:
  node scripts/init-database.js init   - 初始化数据库和示例数据
  node scripts/init-database.js stats - 显示数据库统计信息
  node scripts/init-database.js reset - 重置数据库（删除所有数据）
            `);
            process.exit(1);
    }
}

if (require.main === module) {
    main().catch(error => {
        Logger.error('脚本执行失败:', error);
        process.exit(1);
    });
}

module.exports = DatabaseInitializer;
