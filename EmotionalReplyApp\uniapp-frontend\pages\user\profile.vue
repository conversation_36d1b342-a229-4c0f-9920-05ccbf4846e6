<template>
  <view class="container">
    <view class="header">
      <text class="title">个人资料</text>
    </view>
    
    <view class="form-section">

      <view class="form-item">
        <text class="label">昵称</text>
        <input 
          class="input" 
          v-model="userInfo.nickname" 
          placeholder="请输入昵称"
          @blur="updateNickname"
        />
      </view>
      
      <view class="form-item">
        <text class="label">用户名</text>
        <input 
          class="input readonly" 
          :value="userInfo.username" 
          readonly
          placeholder="用户名不可修改"
        />
      </view>
      
      <view class="form-item">
        <text class="label">邮箱</text>
        <input
          class="input readonly"
          v-model="userInfo.email"
          placeholder="邮箱不可修改"
          readonly
        />
      </view>
      
      <view class="form-item">
        <text class="label">手机号</text>
        <input 
          class="input" 
          v-model="userInfo.phone" 
          placeholder="请输入手机号"
          @blur="updatePhone"
        />
      </view>
      
      <view class="form-item">
        <text class="label">用户类型</text>
        <view class="user-type">
          <text class="type-text" :class="{ vip: userInfo.isVip, admin: userInfo.isAdmin }">
            {{ getUserTypeText }}
          </text>
          <button v-if="showActionButton" :class="actionButtonClass" @click="handleUserAction">
            {{ actionButtonText }}
          </button>
        </view>
      </view>
    </view>
    

    
    <view class="actions">
      <button class="save-btn" @click="saveProfile">保存修改</button>
    </view>
  </view>
</template>

<script>
import { UserManager } from '../../utils/user.js'

export default {
  name: 'UserProfile',
  
  data() {
    return {
      userInfo: {}
    }
  },
  
  computed: {
    // 是否已登录
    isLoggedIn() {
      return UserManager.isLoggedIn()
    },

    // 获取用户类型文本
    getUserTypeText() {
      if (this.userInfo.isAdmin) {
        return '管理员'
      } else if (this.userInfo.isVip) {
        return 'VIP用户'
      } else {
        return '普通用户'
      }
    },

    // 是否显示操作按钮
    showActionButton() {
      // 管理员不显示按钮
      return !this.userInfo.isAdmin
    },

    // 操作按钮文本
    actionButtonText() {
      if (this.userInfo.isVip) {
        return '续费VIP'
      } else {
        return '升级VIP'
      }
    },

    // 操作按钮样式类
    actionButtonClass() {
      return this.userInfo.isVip ? 'renew-btn' : 'upgrade-btn'
    }
  },

  onLoad() {
    // 检查登录状态
    if (!this.isLoggedIn) {
      uni.showModal({
        title: '需要登录',
        content: '请先登录以查看个人资料',
        showCancel: false,
        success: () => {
          uni.navigateTo({
            url: '/pages/login/login'
          })
        }
      })
      return
    }
    this.loadUserInfo()

    // 监听用户信息更新事件
    uni.$on('userInfoUpdated', this.handleUserInfoUpdated)
  },

  onUnload() {
    // 移除事件监听
    uni.$off('userInfoUpdated', this.handleUserInfoUpdated)
  },
  
  methods: {
    // 加载用户信息
    loadUserInfo() {
      const latestUserInfo = UserManager.getUserInfo() || UserManager.getDefaultUserInfo()

      // 使用Vue.set或者直接赋值来确保响应式更新
      this.userInfo = {
        ...latestUserInfo,
        avatar: '/static/images/default-avatar.png'
      }
    },

    // 更新昵称
    updateNickname() {
      if (this.userInfo.nickname && this.userInfo.nickname.trim()) {
        UserManager.updateUserField('nickname', this.userInfo.nickname.trim())
      }
    },

    // 更新手机号
    updatePhone() {
      if (this.userInfo.phone) {
        UserManager.updateUserField('phone', this.userInfo.phone)
      }
    },

    // 处理用户信息更新事件
    handleUserInfoUpdated(data) {
      // 重新加载用户信息
      this.loadUserInfo()

      // 强制触发页面重新渲染
      this.$forceUpdate()

      // 只在重要状态变化时显示提示
      if (data.reason === 'expired') {
        uni.showToast({
          title: 'VIP已到期，配额已降级',
          icon: 'none',
          duration: 3000
        })
      } else if (data.reason === 'sync') {
        uni.showToast({
          title: 'VIP状态已同步',
          icon: 'success',
          duration: 2000
        })
      }
      // forceCheck 不显示提示，避免频繁打扰用户
    },



    // 处理用户操作（升级VIP或续费VIP）
    handleUserAction() {
      if (this.userInfo.isVip) {
        this.renewVip()
      } else {
        this.upgradeVip()
      }
    },

    // 升级VIP
    upgradeVip() {
      // 跳转到VIP升级页面
      uni.navigateTo({
        url: '/pages/vip/upgrade'
      })
    },

    // 续费VIP
    renewVip() {
      // 跳转到VIP续费页面（可以是同一个页面，但显示不同内容）
      uni.navigateTo({
        url: '/pages/vip/upgrade?type=renew'
      })
    },


    
    // 保存资料
    async saveProfile() {
      try {
        uni.showLoading({
          title: '保存中...'
        })

        // 准备要更新的数据
        const profileData = {
          nickname: this.userInfo.nickname,
          phone: this.userInfo.phone
        }

        // 调用后端API更新用户资料
        const { updateUserProfile } = await import('../../api/user.js')
        const currentUserId = UserManager.getCurrentUserId()

        const result = await updateUserProfile(currentUserId, profileData)

        // 更新本地用户信息
        UserManager.setUserInfo(result)

        uni.hideLoading()
        uni.showToast({
          title: '保存成功',
          icon: 'success'
        })

        // 返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)

      } catch (error) {
        uni.hideLoading()
        console.error('保存用户资料失败:', error)
        uni.showToast({
          title: error.message || '保存失败',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.header {
  background: linear-gradient(135deg, #2196F3, #21CBF3);
  padding: 40rpx 30rpx;
  text-align: center;
  
  .title {
    color: white;
    font-size: 36rpx;
    font-weight: bold;
  }
}

.form-section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  
  .form-item {
    display: flex;
    align-items: center;
    padding: 30rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .label {
      width: 150rpx;
      font-size: 28rpx;
      color: #333;
    }
    
    .input {
      flex: 1;
      height: 60rpx;
      padding: 0 20rpx;
      border: 1rpx solid #e0e0e0;
      border-radius: 8rpx;
      font-size: 28rpx;
      
      &.readonly {
        background-color: #f5f5f5;
        color: #999;
      }
    }
    

    
    .user-type {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .type-text {
        font-size: 28rpx;
        color: #666;
        
        &.vip {
          color: #ff6b35;
          font-weight: bold;
        }

        &.admin {
          color: #9c27b0;
          font-weight: bold;
        }
      }
      
      .upgrade-btn {
        background: #ff6b35;
        color: white;
        border: none;
        border-radius: 20rpx;
        padding: 10rpx 20rpx;
        font-size: 24rpx;
      }

      .renew-btn {
        background: #4caf50;
        color: white;
        border: none;
        border-radius: 20rpx;
        padding: 10rpx 20rpx;
        font-size: 24rpx;
      }
    }
  }
}

.actions {
  padding: 40rpx 30rpx;
  
  .save-btn {
    width: 100%;
    height: 88rpx;
    background: #2196F3;
    color: white;
    border: none;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
  }
}
</style>
