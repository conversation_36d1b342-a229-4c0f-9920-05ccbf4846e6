package com.emotional.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.emotional.service.dto.request.ChangePasswordRequest;
import com.emotional.service.dto.request.UpdateUserProfileRequest;
import com.emotional.service.dto.request.UserLoginRequest;
import com.emotional.service.dto.request.UserRegisterRequest;
import com.emotional.service.dto.response.UserLoginResponse;
import com.emotional.service.entity.User;
import com.emotional.service.mapper.UserMapper;
import com.emotional.service.service.EmailService;
import com.emotional.service.service.UserService;
import com.emotional.service.service.VerificationCodeService;
import com.emotional.service.utils.JwtUtils;
import com.emotional.service.utils.PasswordValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Service
@Slf4j
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {
    
    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private EmailService emailService;

    @Autowired
    private VerificationCodeService verificationCodeService;

    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    
    @Override
    public UserLoginResponse login(UserLoginRequest request, String clientIp, String userAgent) {
        // 只通过邮箱查找用户
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("email", request.getUsername())
                   .eq("deleted", 0);

        User user = this.getOne(queryWrapper);

        if (user == null) {
            throw new RuntimeException("邮箱不存在或未注册");
        }
        
        // 检查用户状态
        if (user.getStatus() != null && user.getStatus() != 0) {
            throw new RuntimeException("用户已被禁用");
        }
        
        // 验证密码
        if (!verifyPassword(request.getPassword(), user.getPassword())) {
            throw new RuntimeException("密码错误");
        }
        
        // 更新登录信息
        updateLoginInfo(user.getId(), clientIp, userAgent);
        
        // 生成JWT令牌
        String token = generateToken(user);
        
        // 构建响应
        UserLoginResponse response = new UserLoginResponse();
        response.setToken(token);
        response.setTokenType("Bearer");
        response.setExpiresIn(86400L); // 24小时
        
        // 设置用户信息
        UserLoginResponse.UserInfo userInfo = new UserLoginResponse.UserInfo();
        BeanUtils.copyProperties(user, userInfo);
        userInfo.setIsVip(user.getIsVip() != null && user.getIsVip() == 1);
        userInfo.setIsAdmin(user.getIsAdmin() != null && user.getIsAdmin() == 1);
        response.setUserInfo(userInfo);
        
        return response;
    }
    
    @Override
    public User register(UserRegisterRequest request) {
        // 检查用户名是否已存在
        QueryWrapper<User> usernameQuery = new QueryWrapper<>();
        usernameQuery.eq("username", request.getUsername()).eq("deleted", 0);
        if (this.count(usernameQuery) > 0) {
            throw new RuntimeException("用户名已存在");
        }
        
        // 检查邮箱是否已存在
        if (StringUtils.hasText(request.getEmail())) {
            QueryWrapper<User> emailQuery = new QueryWrapper<>();
            emailQuery.eq("email", request.getEmail()).eq("deleted", 0);
            if (this.count(emailQuery) > 0) {
                throw new RuntimeException("邮箱已被使用");
            }
        }
        
        // 检查手机号是否已存在
        if (StringUtils.hasText(request.getPhone())) {
            QueryWrapper<User> phoneQuery = new QueryWrapper<>();
            phoneQuery.eq("phone", request.getPhone()).eq("deleted", 0);
            if (this.count(phoneQuery) > 0) {
                throw new RuntimeException("手机号已被使用");
            }
        }

        // 验证密码强度
        PasswordValidator.ValidationResult passwordValidation = PasswordValidator.validate(request.getPassword());
        if (!passwordValidation.isValid()) {
            throw new RuntimeException(passwordValidation.getMessage());
        }

        // 创建新用户
        User user = new User();
        user.setUsername(request.getUsername());
        user.setNickname(request.getNickname());
        user.setEmail(request.getEmail());
        user.setPhone(request.getPhone());
        user.setPassword(encodePassword(request.getPassword()));
        user.setStatus(0); // 正常状态
        user.setIsVip(0); // 普通用户
        user.setIsAdmin(0); // 非管理员
        user.setDailyQuota(10); // 默认每日配额
        user.setTodayUsed(0);
        user.setTotalUsed(0);
        user.setDeleted(0);
        
        // 保存用户
        if (!this.save(user)) {
            throw new RuntimeException("注册失败");
        }
        
        return user;
    }
    
    @Override
    public User getUserByUsername(String username) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", username).eq("deleted", 0);
        return this.getOne(queryWrapper);
    }
    
    @Override
    public User getUserByEmail(String email) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("email", email).eq("deleted", 0);
        return this.getOne(queryWrapper);
    }

    @Override
    public User getUserById(Long userId) {
        return this.getById(userId);
    }

    @Override
    public boolean updateUserInfo(Long userId, User userInfo) {
        User existingUser = this.getById(userId);
        if (existingUser == null) {
            throw new RuntimeException("用户不存在");
        }

        // 只更新允许修改的字段
        if (StringUtils.hasText(userInfo.getNickname())) {
            existingUser.setNickname(userInfo.getNickname());
        }
        if (StringUtils.hasText(userInfo.getEmail())) {
            // 检查邮箱是否已被其他用户使用
            QueryWrapper<User> emailQuery = new QueryWrapper<>();
            emailQuery.eq("email", userInfo.getEmail())
                     .eq("deleted", 0)
                     .ne("id", userId);
            if (this.count(emailQuery) > 0) {
                throw new RuntimeException("邮箱已被其他用户使用");
            }
            existingUser.setEmail(userInfo.getEmail());
        }
        if (StringUtils.hasText(userInfo.getPhone())) {
            // 检查手机号是否已被其他用户使用
            QueryWrapper<User> phoneQuery = new QueryWrapper<>();
            phoneQuery.eq("phone", userInfo.getPhone())
                     .eq("deleted", 0)
                     .ne("id", userId);
            if (this.count(phoneQuery) > 0) {
                throw new RuntimeException("手机号已被其他用户使用");
            }
            existingUser.setPhone(userInfo.getPhone());
        }
        if (StringUtils.hasText(userInfo.getAvatar())) {
            existingUser.setAvatar(userInfo.getAvatar());
        }

        return this.updateById(existingUser);
    }
    
    @Override
    public boolean changePassword(Long userId, String oldPassword, String newPassword) {
        User user = this.getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        // 验证旧密码
        if (!verifyPassword(oldPassword, user.getPassword())) {
            throw new RuntimeException("原密码错误");
        }

        // 验证新密码强度
        PasswordValidator.ValidationResult passwordValidation = PasswordValidator.validate(newPassword);
        if (!passwordValidation.isValid()) {
            throw new RuntimeException(passwordValidation.getMessage());
        }

        // 更新密码
        user.setPassword(encodePassword(newPassword));
        return this.updateById(user);
    }
    
    @Override
    public boolean verifyPassword(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }
    
    @Override
    public String encodePassword(String rawPassword) {
        return passwordEncoder.encode(rawPassword);
    }
    
    @Override
    public String generateToken(User user) {
        return jwtUtils.generateToken(user.getId(), user.getUsername());
    }
    
    @Override
    public void updateLoginInfo(Long userId, String clientIp, String userAgent) {
        User user = new User();
        user.setId(userId);
        user.setLastLoginTime(LocalDateTime.now());
        user.setLastLoginIp(clientIp);
        this.updateById(user);

        log.info("更新用户登录信息: userId={}, ip={}", userId, clientIp);
    }

    @Override
    public boolean checkUserExists(String email) {
        log.info("开始检查邮箱是否存在: email={}", email);

        QueryWrapper<User> queryWrapper = new QueryWrapper<>();

        // 简化查询条件，只检查邮箱和删除状态
        queryWrapper.eq("email", email)
                   .eq("deleted", 0);

        long count = this.count(queryWrapper);

        log.info("邮箱检查结果: email={}, count={}, exists={}", email, count, count > 0);

        // 如果找到用户，也打印用户信息用于调试
        if (count > 0) {
            User user = this.getOne(queryWrapper);
            log.info("找到用户: id={}, username={}, email={}",
                user.getId(), user.getUsername(), user.getEmail());
        }

        return count > 0;
    }

    @Override
    public boolean sendResetPasswordCode(String identifier) {
        // 注意：用户存在性检查已在前端步骤1完成，这里不再重复检查
        log.info("开始发送重置密码验证码: identifier={}", identifier);

        // 生成验证码并存储到Redis
        String code = verificationCodeService.generateResetCode(identifier);

        // 发送邮件验证码
        boolean emailSent = emailService.sendPasswordResetCode(identifier, code);

        if (emailSent) {
            log.info("密码重置验证码邮件发送成功: identifier={}, code={}", identifier, code);
        } else {
            log.error("密码重置验证码邮件发送失败: identifier={}, code={}", identifier, code);
            // 发送失败时删除验证码
            verificationCodeService.deleteCode(identifier, "reset");
            throw new RuntimeException("验证码发送失败，请稍后重试");
        }

        return emailSent;
    }

    @Override
    public boolean verifyResetCode(String identifier, String code) {
        log.info("验证重置验证码: identifier={}, code={}", identifier, code);

        return verificationCodeService.verifyResetCode(identifier, code);
    }

    @Override
    public boolean resetPassword(String email, String code, String newPassword) {
        log.info("开始重置密码: email={}, code={}", email, code);

        // 验证验证码并消费（标记为已使用）
        if (!verificationCodeService.verifyResetCode(email, code, true)) {
            log.error("验证码验证失败: email={}", email);
            throw new RuntimeException("验证码验证失败");
        }

        log.info("验证码验证成功: email={}", email);

        // 查找用户（只通过邮箱）
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("email", email)
                   .eq("deleted", 0);

        User user = this.getOne(queryWrapper);

        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 验证新密码强度
        PasswordValidator.ValidationResult passwordValidation = PasswordValidator.validate(newPassword);
        if (!passwordValidation.isValid()) {
            throw new RuntimeException(passwordValidation.getMessage());
        }

        // 更新密码
        user.setPassword(encodePassword(newPassword));
        boolean success = this.updateById(user);

        if (success) {
            // 删除验证码
            verificationCodeService.deleteCode(email, "reset");
            log.info("用户密码重置成功: email={}", email);
        }

        return success;
    }

    @Override
    public boolean sendRegisterCode(String email) {
        log.info("发送注册验证码: email={}", email);

        try {
            // 生成验证码并存储到Redis
            String code = verificationCodeService.generateRegisterCode(email);

            // 发送邮件
            boolean sent = emailService.sendVerificationCode(email, code);

            if (sent) {
                log.info("注册验证码发送成功: email={}, code={}", email, code);
            } else {
                log.warn("注册验证码发送失败: email={}", email);
                verificationCodeService.deleteCode(email, "register");
            }

            return sent;

        } catch (Exception e) {
            log.error("发送注册验证码异常: email={}", email, e);
            return false;
        }
    }

    @Override
    public boolean verifyRegisterCode(String email, String code) {
        log.info("验证注册验证码: email={}, code={}", email, code);

        return verificationCodeService.verifyRegisterCode(email, code);
    }

    @Override
    public User updateUserProfile(Long userId, UpdateUserProfileRequest request) {
        log.info("更新用户资料: userId={}, request={}", userId, request);

        try {
            // 获取当前用户
            User user = this.getById(userId);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }

            // 检查邮箱是否被其他用户使用
            if (request.getEmail() != null && !request.getEmail().equals(user.getEmail())) {
                QueryWrapper<User> emailQuery = new QueryWrapper<>();
                emailQuery.eq("email", request.getEmail())
                         .eq("deleted", 0)
                         .ne("id", userId);
                if (this.count(emailQuery) > 0) {
                    throw new RuntimeException("邮箱已被其他用户使用");
                }
            }

            // 检查手机号是否被其他用户使用
            if (request.getPhone() != null && !request.getPhone().equals(user.getPhone())) {
                QueryWrapper<User> phoneQuery = new QueryWrapper<>();
                phoneQuery.eq("phone", request.getPhone())
                         .eq("deleted", 0)
                         .ne("id", userId);
                if (this.count(phoneQuery) > 0) {
                    throw new RuntimeException("手机号已被其他用户使用");
                }
            }

            // 更新用户信息
            if (request.getNickname() != null) {
                user.setNickname(request.getNickname());
            }
            if (request.getEmail() != null) {
                user.setEmail(request.getEmail());
            }
            if (request.getPhone() != null) {
                user.setPhone(request.getPhone());
            }
            if (request.getAvatar() != null) {
                user.setAvatar(request.getAvatar());
            }

            // 保存更新
            if (!this.updateById(user)) {
                throw new RuntimeException("更新用户资料失败");
            }

            log.info("用户资料更新成功: userId={}", userId);
            return user;

        } catch (Exception e) {
            log.error("更新用户资料失败: userId={}", userId, e);
            throw new RuntimeException("更新用户资料失败: " + e.getMessage());
        }
    }

    @Override
    public boolean changeUserPassword(Long userId, ChangePasswordRequest request) {
        log.info("修改用户密码: userId={}", userId);

        try {
            // 验证两次密码是否一致
            if (!request.isPasswordMatch()) {
                throw new RuntimeException("两次密码输入不一致");
            }

            // 获取当前用户
            User user = this.getById(userId);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }

            // 验证旧密码
            if (!verifyPassword(request.getOldPassword(), user.getPassword())) {
                throw new RuntimeException("旧密码错误");
            }

            // 检查新密码是否与旧密码相同
            if (verifyPassword(request.getNewPassword(), user.getPassword())) {
                throw new RuntimeException("新密码不能与旧密码相同");
            }

            // 更新密码
            user.setPassword(encodePassword(request.getNewPassword()));

            // 保存更新
            if (!this.updateById(user)) {
                throw new RuntimeException("修改密码失败");
            }

            log.info("用户密码修改成功: userId={}", userId);
            return true;

        } catch (Exception e) {
            log.error("修改用户密码失败: userId={}", userId, e);
            throw new RuntimeException("修改密码失败: " + e.getMessage());
        }
    }

    @Override
    public boolean updateUserVipInfo(Long userId, Integer isVip, LocalDateTime vipExpireTime, Integer dailyQuota) {
        try {
            log.info("更新用户VIP信息: userId={}, isVip={}, vipExpireTime={}, dailyQuota={}",
                    userId, isVip, vipExpireTime, dailyQuota);

            int updated = baseMapper.updateUserVipInfo(userId, isVip, vipExpireTime, dailyQuota);

            if (updated > 0) {
                log.info("用户VIP信息更新成功: userId={}, affected={}", userId, updated);
                return true;
            } else {
                log.warn("用户VIP信息更新失败，没有影响任何行: userId={}", userId);
                return false;
            }

        } catch (Exception e) {
            log.error("更新用户VIP信息失败: userId={}", userId, e);
            return false;
        }
    }

    @Override
    public List<User> getAllVipUsers() {
        try {
            QueryWrapper<User> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("is_vip", 1)
                       .eq("deleted", 0);

            List<User> vipUsers = list(queryWrapper);
            log.info("查询到{}个VIP用户", vipUsers.size());
            return vipUsers;

        } catch (Exception e) {
            log.error("获取VIP用户列表失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public int batchDowngradeExpiredVipUsers() {
        try {
            log.info("开始批量降级过期VIP用户");
            int affectedRows = baseMapper.batchDowngradeExpiredVipUsers();
            log.info("批量降级完成，影响{}个用户", affectedRows);
            return affectedRows;
        } catch (Exception e) {
            log.error("批量降级过期VIP用户失败", e);
            return 0;
        }
    }

    @Override
    public List<User> getExpiredVipUsersBatch(int limit, int offset) {
        try {
            List<User> expiredUsers = baseMapper.getExpiredVipUsersBatch(limit, offset);
            log.debug("获取到{}个过期VIP用户，偏移量: {}", expiredUsers.size(), offset);
            return expiredUsers;
        } catch (Exception e) {
            log.error("分批获取过期VIP用户失败: limit={}, offset={}", limit, offset, e);
            return new ArrayList<>();
        }
    }

}
