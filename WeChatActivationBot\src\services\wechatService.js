const crypto = require('crypto');
const xml2js = require('xml2js');
const Logger = require('../utils/logger');

class WeChatService {
    constructor(token) {
        this.token = token;
        this.parser = new xml2js.Parser({ explicitArray: false });
    }

    /**
     * 验证微信服务器签名
     */
    verifySignature(signature, timestamp, nonce) {
        const tmpArr = [this.token, timestamp, nonce];
        tmpArr.sort();
        const tmpStr = tmpArr.join('');
        const shasum = crypto.createHash('sha1');
        shasum.update(tmpStr);
        const hashCode = shasum.digest('hex');
        
        return hashCode === signature;
    }

    /**
     * 解析微信XML消息
     */
    async parseMessage(xmlData) {
        try {
            const result = await this.parser.parseStringPromise(xmlData);
            return result.xml;
        } catch (error) {
            Logger.error('解析XML消息失败:', error);
            throw new Error('消息解析失败');
        }
    }

    /**
     * 创建文本回复消息
     */
    createTextResponse(toUser, fromUser, content) {
        const timestamp = Math.floor(Date.now() / 1000);
        
        const response = `
        <xml>
            <ToUserName><![CDATA[${toUser}]]></ToUserName>
            <FromUserName><![CDATA[${fromUser}]]></FromUserName>
            <CreateTime>${timestamp}</CreateTime>
            <MsgType><![CDATA[text]]></MsgType>
            <Content><![CDATA[${content}]]></Content>
        </xml>`;
        
        return response.trim();
    }

    /**
     * 创建图文回复消息
     */
    createNewsResponse(toUser, fromUser, articles) {
        const timestamp = Math.floor(Date.now() / 1000);
        const articleCount = articles.length;
        
        let articlesXml = '';
        articles.forEach(article => {
            articlesXml += `
            <item>
                <Title><![CDATA[${article.title}]]></Title>
                <Description><![CDATA[${article.description}]]></Description>
                <PicUrl><![CDATA[${article.picUrl}]]></PicUrl>
                <Url><![CDATA[${article.url}]]></Url>
            </item>`;
        });

        const response = `
        <xml>
            <ToUserName><![CDATA[${toUser}]]></ToUserName>
            <FromUserName><![CDATA[${fromUser}]]></FromUserName>
            <CreateTime>${timestamp}</CreateTime>
            <MsgType><![CDATA[news]]></MsgType>
            <ArticleCount>${articleCount}</ArticleCount>
            <Articles>${articlesXml}</Articles>
        </xml>`;
        
        return response.trim();
    }

    /**
     * 验证消息是否来自微信服务器
     */
    validateMessage(message) {
        const requiredFields = ['ToUserName', 'FromUserName', 'CreateTime', 'MsgType'];
        return requiredFields.every(field => message[field]);
    }

    /**
     * 获取用户信息（需要access_token）
     */
    async getUserInfo(openid, accessToken) {
        try {
            const axios = require('axios');
            const url = `https://api.weixin.qq.com/cgi-bin/user/info?access_token=${accessToken}&openid=${openid}&lang=zh_CN`;
            
            const response = await axios.get(url);
            return response.data;
        } catch (error) {
            Logger.error('获取用户信息失败:', error);
            return null;
        }
    }
}

module.exports = WeChatService;
