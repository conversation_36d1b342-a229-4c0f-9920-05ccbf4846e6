package com.emotional.service.task;

import com.emotional.service.entity.User;
import com.emotional.service.service.UserService;
import com.emotional.service.service.UserStatsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * VIP过期检查定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class VipExpirationTask {

    private final UserService userService;
    private final UserStatsService userStatsService;

    /**
     * 检查VIP过期用户并降级配额
     * 每天凌晨3点执行一次，主要处理不活跃用户
     * 活跃用户的VIP过期由前端页面检查处理
     */
    @Scheduled(cron = "0 0 3 * * ?")
    public void checkAndDowngradeExpiredVipUsers() {
        try {
            log.info("开始批量检查VIP过期用户...");

            // 先更新用户统计表（基于users表中的VIP状态）
            boolean statsUpdated = userStatsService.batchUpdateExpiredVipUsersStats();

            // 再批量降级用户表中的过期用户
            int expiredCount = userService.batchDowngradeExpiredVipUsers();

            if (expiredCount > 0) {
                log.info("VIP过期检查完成: 降级了{}个过期用户，统计表更新: {}", expiredCount, statsUpdated);
            } else {
                log.info("没有过期的VIP用户需要处理");
            }

        } catch (Exception e) {
            log.error("VIP过期检查任务执行失败", e);
        }
    }

    /**
     * 分批处理大量过期用户（备用方案）
     * 每天凌晨2点执行一次，处理可能遗漏的用户
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void batchProcessExpiredVipUsers() {
        try {
            log.info("开始分批处理过期VIP用户...");

            final int BATCH_SIZE = 1000; // 每批处理1000个用户
            int totalProcessed = 0;
            int batchCount = 0;

            while (true) {
                // 分批获取过期VIP用户
                List<User> expiredUsers = userService.getExpiredVipUsersBatch(BATCH_SIZE, batchCount * BATCH_SIZE);

                if (expiredUsers.isEmpty()) {
                    break; // 没有更多用户需要处理
                }

                // 批量处理这批用户
                for (User user : expiredUsers) {
                    try {
                        downgradeExpiredVipUser(user);
                        totalProcessed++;
                    } catch (Exception e) {
                        log.error("处理用户VIP过期失败: userId={}", user.getId(), e);
                    }
                }

                batchCount++;

                // 避免长时间占用数据库连接，每批之间休息100ms
                Thread.sleep(100);

                log.info("已处理第{}批，本批{}个用户", batchCount, expiredUsers.size());
            }

            log.info("分批处理完成: 总共处理了{}个过期用户", totalProcessed);

        } catch (Exception e) {
            log.error("分批处理过期VIP用户失败", e);
        }
    }

    /**
     * 降级过期VIP用户
     */
    private void downgradeExpiredVipUser(User user) {
        try {
            log.info("降级过期VIP用户: userId={}, expireTime={}", user.getId(), user.getVipExpireTime());
            
            // 更新用户表
            user.setIsVip(0);
            user.setDailyQuota(3); // 恢复普通用户配额
            boolean updateSuccess = userService.updateById(user);
            
            if (!updateSuccess) {
                log.error("更新过期VIP用户失败: userId={}", user.getId());
                return;
            }
            
            // 更新用户统计表
            try {
                userStatsService.updateUserQuotaAfterVipExpired(user.getId());
                log.info("过期VIP用户降级成功: userId={}, 配额已恢复为3", user.getId());
            } catch (Exception e) {
                log.warn("更新过期VIP用户统计表失败: userId={}, error={}", user.getId(), e.getMessage());
            }
            
        } catch (Exception e) {
            log.error("降级过期VIP用户失败: userId={}", user.getId(), e);
        }
    }
}
