# 本地开发指南

## 🏠 本地使用说明

### ⚠️ 重要提醒
微信公众号需要外网可访问的服务器URL，所以**完全本地环境无法直接接收微信消息**。但是我们提供了多种本地开发和测试方案。

## 🚀 本地开发方案

### 方案1: 完全本地测试（推荐）

这种方式可以测试所有核心功能，不需要微信公众号：

```bash
# 1. 安装依赖
npm install

# 2. 配置环境（可以使用默认配置）
cp .env.example .env

# 3. 初始化数据库
npm run init-db init

# 4. 运行自动化测试
npm run local-test

# 5. 运行交互式测试
npm run local-test-interactive
```

#### 自动化测试功能
- ✅ 关键字匹配测试
- ✅ 激活码生成和分配
- ✅ 多用户场景模拟
- ✅ 重复请求处理
- ✅ 统计信息查看

#### 交互式测试
```bash
npm run local-test-interactive
```
然后输入关键字，系统会模拟微信机器人的回复。

### 方案2: 本地服务器 + 内网穿透

如果想测试真实的微信消息，可以使用内网穿透工具：

#### 使用 ngrok
```bash
# 1. 安装 ngrok
npm install -g ngrok

# 2. 启动本地服务
npm run dev

# 3. 在另一个终端启动 ngrok
ngrok http 3000
```

ngrok 会提供一个公网URL，如：`https://abc123.ngrok.io`

#### 配置微信公众号
在微信公众平台设置：
- URL: `https://abc123.ngrok.io/wechat`
- Token: 你在 `.env` 中设置的 `WECHAT_TOKEN`

#### 使用 localtunnel
```bash
# 1. 安装 localtunnel
npm install -g localtunnel

# 2. 启动本地服务
npm run dev

# 3. 创建隧道
lt --port 3000 --subdomain your-bot-name
```

### 方案3: 本地API测试

启动本地服务后，可以直接测试管理API：

```bash
# 启动服务
npm run dev

# 测试健康检查
curl http://localhost:3000/health

# 查看关键字
curl http://localhost:3000/admin/keywords

# 查看统计信息
curl http://localhost:3000/admin/stats
```

## 📋 本地测试步骤

### 1. 环境准备
```bash
cd WeChatActivationBot
npm install
cp .env.example .env
```

### 2. 数据库初始化
```bash
# 初始化数据库并创建示例数据
npm run init-db init

# 查看数据库统计
npm run init-db stats
```

### 3. 功能测试

#### A. 自动化测试
```bash
npm run local-test
```

输出示例：
```
=== 测试关键字匹配 ===
✅ 关键字 "VIP会员" 匹配成功: VIP会员 (ID: 1)
✅ 关键字 "高级版" 匹配成功: 高级版 (ID: 2)

=== 测试激活码生成 ===
关键字 "VIP会员": 总计 10 个，已用 0 个，可用 10 个

=== 模拟用户交互 ===
用户 test_user_001 发送消息: "VIP会员"
✅ 匹配到关键字: VIP会员
✅ 分配激活码: ABC123DEF456
✅ 回复内容: 您的激活码是：ABC123DEF456
```

#### B. 交互式测试
```bash
npm run local-test-interactive
```

然后可以输入关键字进行测试：
```
🤖 微信激活码机器人 - 交互式测试
请输入关键字: VIP会员

🤖 机器人回复: 您的激活码是：XYZ789ABC123

请妥善保管，激活码仅可使用一次。
```

#### C. API测试
```bash
# 启动服务
npm run dev

# 在另一个终端测试API
curl -X GET http://localhost:3000/admin/keywords
curl -X GET http://localhost:3000/admin/stats
```

### 4. 管理功能测试

#### 添加新关键字
```bash
curl -X POST http://localhost:3000/admin/keywords \
  -H "Content-Type: application/json" \
  -d '{"keyword": "新产品", "description": "新产品激活码"}'
```

#### 生成激活码
```bash
curl -X POST http://localhost:3000/admin/keywords/1/codes/generate \
  -H "Content-Type: application/json" \
  -d '{"count": 20}'
```

#### 查看激活码统计
```bash
curl http://localhost:3000/admin/keywords/1/stats
```

## 🔧 开发工具

### 1. 数据库管理
```bash
# 查看数据库状态
npm run init-db stats

# 重置数据库（清空所有数据）
npm run init-db reset
```

### 2. 日志查看
```bash
# 查看应用日志
tail -f logs/app.log

# 或者在开发模式下直接在控制台查看
npm run dev
```

### 3. 单元测试
```bash
npm test
```

## 🐛 常见问题

### Q: 本地测试时数据库错误
A: 确保有写权限，运行：
```bash
mkdir -p data logs
npm run init-db init
```

### Q: 想要重新开始测试
A: 重置数据库：
```bash
npm run init-db reset
```

### Q: 如何添加自定义关键字？
A: 有两种方式：
1. 通过API：
```bash
curl -X POST http://localhost:3000/admin/keywords \
  -H "Content-Type: application/json" \
  -d '{"keyword": "我的关键字", "description": "描述"}'
```

2. 直接修改 `scripts/init-database.js` 中的示例数据

### Q: 激活码格式可以自定义吗？
A: 可以，修改 `src/services/activationCodeManager.js` 中的 `generateRandomCode` 方法

## 📈 性能测试

### 压力测试
```bash
# 安装压力测试工具
npm install -g autocannon

# 启动服务
npm run dev

# 压力测试健康检查接口
autocannon -c 10 -d 30 http://localhost:3000/health

# 压力测试关键字查询
autocannon -c 10 -d 30 http://localhost:3000/admin/keywords
```

## 🚀 准备部署

当本地测试完成后，可以准备部署：

1. **环境变量配置**: 确保生产环境的 `.env` 配置正确
2. **数据库备份**: 备份本地测试的数据库
3. **性能优化**: 根据测试结果调整配置
4. **安全检查**: 确保敏感信息不会泄露

## 📝 开发建议

1. **先本地测试**: 使用 `npm run local-test` 验证核心功能
2. **API测试**: 使用 Postman 或 curl 测试管理接口
3. **单元测试**: 运行 `npm test` 确保代码质量
4. **日志监控**: 开发时注意查看日志输出
5. **数据备份**: 定期备份测试数据

这样您就可以在完全本地的环境中开发和测试微信激活码机器人的所有功能了！
