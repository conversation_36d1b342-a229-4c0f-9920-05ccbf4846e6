/**
 * 用户管理工具类
 */

import { register } from '../api/user.js'

// 基础配置 - 从request.js中获取
const getBaseURL = () => {
  // #ifdef H5
  return 'http://localhost:8080'
  // #endif

  // #ifdef APP-PLUS || MP-WEIXIN
  return 'http://*************:8080'
  // #endif

  return 'http://localhost:8080'
}

const config = {
  baseURL: getBaseURL()
}

// 用户信息存储键
const USER_INFO_KEY = 'userInfo'
const USER_TOKEN_KEY = 'userToken'
const CURRENT_USER_ID_KEY = 'currentUserId'

/**
 * 用户管理器
 */
export class UserManager {

  // 防抖相关的静态变量
  static _lastVipCheckTime = 0
  static _vipCheckDebounceDelay = 5000 // 5秒内不重复检查
  
  /**
   * 设置当前用户信息
   * @param {object} userInfo - 用户信息
   */
  static setUserInfo(userInfo) {
    try {
      uni.setStorageSync(USER_INFO_KEY, userInfo)
      if (userInfo.id) {
        uni.setStorageSync(CURRENT_USER_ID_KEY, userInfo.id)
      }
    } catch (error) {
    }
  }
  
  /**
   * 获取当前用户信息
   * @returns {object|null} 用户信息
   */
  static getUserInfo() {
    try {
      const userInfo = uni.getStorageSync(USER_INFO_KEY)

      // 确保头像始终使用默认头像
      if (userInfo) {
        userInfo.avatar = '/static/images/default-avatar.png'
      }

      return userInfo || null
    } catch (error) {
      return null
    }
  }
  
  /**
   * 获取当前用户ID
   * @returns {number|null} 用户ID
   */
  static getCurrentUserId() {
    try {
      const userId = uni.getStorageSync(CURRENT_USER_ID_KEY)
      return userId || 1 // 默认返回1作为测试用户ID
    } catch (error) {
      return 1
    }
  }
  
  /**
   * 设置用户Token
   * @param {string} token - 用户Token
   */
  static setToken(token) {
    try {
      uni.setStorageSync(USER_TOKEN_KEY, token)
    } catch (error) {
    }
  }
  
  /**
   * 获取用户Token
   * @returns {string|null} Token
   */
  static getToken() {
    try {
      return uni.getStorageSync(USER_TOKEN_KEY)
    } catch (error) {
      return null
    }
  }
  
  /**
   * 清除用户信息
   */
  static clearUserInfo() {
    try {
      uni.removeStorageSync(USER_INFO_KEY)
      uni.removeStorageSync(USER_TOKEN_KEY)
      uni.removeStorageSync(CURRENT_USER_ID_KEY)
    } catch (error) {
      console.error('清除用户信息失败:', error)
    }
  }

  /**
   * 退出登录
   */
  static logout() {
    this.clearUserInfo()

    // 跳转到登录页
    uni.reLaunch({
      url: '/pages/login/login'
    })
  }
  
  /**
   * 检查是否已登录
   * @returns {boolean} 是否已登录
   */
  static isLoggedIn() {
    const userInfo = this.getUserInfo()
    const token = this.getToken()
    return !!(userInfo && token)
  }
  
  /**
   * 获取默认用户信息（用于测试）
   * @returns {object} 默认用户信息
   */
  static getDefaultUserInfo() {
    return {
      id: 1,
      username: 'testuser',
      nickname: '测试用户',
      email: '<EMAIL>',
      avatar: '/static/images/default-avatar.png',
      isVip: false,
      isAdmin: false,
      dailyQuota: 10,
      todayUsed: 0,
      totalUsed: 0
    }
  }
  
  /**
   * 初始化默认用户（仅在开发模式下使用）
   */
  static initDefaultUser() {
    // 只在开发环境或明确需要时初始化测试用户
    // 正常情况下不自动初始化，保持未登录状态
  }

  /**
   * 强制初始化测试用户（仅用于开发调试）
   */
  static forceInitTestUser() {
    const defaultUser = this.getDefaultUserInfo()
    this.setUserInfo(defaultUser)
    this.setToken('test_token_' + Date.now())
  }
  
  /**
   * 更新用户信息的某个字段
   * @param {string} key - 字段名
   * @param {any} value - 字段值
   */
  static updateUserField(key, value) {
    const userInfo = this.getUserInfo()
    if (userInfo) {
      userInfo[key] = value
      this.setUserInfo(userInfo)

      // 如果更新的是VIP相关字段，清除配额缓存
      if (key === 'isVip' || key === 'vipExpireTime') {
        this.clearQuotaCache()
      }
    }
  }

  /**
   * 清除配额缓存
   */
  static clearQuotaCache() {
    try {
      uni.removeStorageSync('userQuota')
      uni.removeStorageSync('quotaLastUpdate')
    } catch (error) {
      console.error('清除配额缓存失败:', error)
    }
  }

  /**
   * 强制刷新用户配额
   */
  static async refreshUserQuota() {
    try {
      // 清除缓存
      this.clearQuotaCache()

      // 重新获取配额（这会触发API调用）
      const { getUserQuota } = await import('../api/user.js')
      const userId = this.getCurrentUserId()

      if (userId) {
        const result = await getUserQuota(userId)
        if (result && result.success) {
          return result.data
        }
      }
    } catch (error) {
      console.error('刷新用户配额失败:', error)
    }
    return null
  }

  /**
   * 检查VIP是否到期
   * @returns {boolean} true表示VIP已到期，false表示VIP有效或不是VIP用户
   */
  static checkVipExpired() {
    const userInfo = this.getUserInfo()
    if (!userInfo) {
      return false // 没有用户信息
    }

    // 如果当前不是VIP用户，检查是否有过期时间（表示曾经是VIP）
    if (!userInfo.isVip || userInfo.isVip !== 1) {
      // 如果有过期时间，说明曾经是VIP，现在可能已过期
      // 如果没有过期时间，说明从未是VIP，不需要提示过期
      return userInfo.vipExpireTime && userInfo.vipExpireTime !== null
    }

    // 当前是VIP用户，检查是否过期
    if (!userInfo.vipExpireTime) {
      return true // VIP用户但没有过期时间，认为已过期
    }

    try {
      const expireTime = new Date(userInfo.vipExpireTime)
      const now = new Date()
      const isExpired = expireTime <= now

      if (isExpired) {
        // VIP已过期
      }

      return isExpired
    } catch (error) {
      console.error('检查VIP过期时间失败:', error)
      return true // 出错时认为已过期
    }
  }

  /**
   * 处理VIP到期
   */
  static async handleVipExpired() {
    try {
      const userInfo = this.getUserInfo()
      if (!userInfo) {
        return
      }

      // 判断是否应该显示过期弹窗
      // 只有当前是VIP用户或者曾经是VIP用户（有过期时间）才显示弹窗
      const shouldShowExpiredModal = userInfo.isVip === 1 || (userInfo.vipExpireTime && userInfo.vipExpireTime !== null)

      // 重要：先调用后端VIP检查接口，触发服务器端数据更新
      if (userInfo.id) {
        try {
          const response = await uni.request({
            url: `${config.baseURL}/api/user/vip-status`,
            method: 'GET',
            data: { userId: userInfo.id },
            header: {
              'Authorization': `Bearer ${this.getToken()}`
            }
          })

          if (response.data && response.data.code === 200) {
            // 后端VIP状态检查完成
          }
        } catch (error) {
          console.error('调用后端VIP检查失败:', error)
        }
      }

      // 更新本地用户信息
      this.updateUserField('isVip', 0)
      this.updateUserField('vipExpireTime', null)

      // 重要：强制刷新配额信息，确保从服务器获取最新的普通用户配额
      this.clearQuotaCache()
      await this.refreshUserQuota()

      // 通知其他页面用户信息已更新
      uni.$emit('userInfoUpdated', {
        isVip: false,
        vipExpireTime: null,
        reason: 'expired'
      })

      // 只有真正的VIP过期用户才显示弹窗提示
      if (shouldShowExpiredModal) {
        uni.showModal({
          title: 'VIP已到期',
          content: '您的VIP会员已到期，配额已恢复为普通用户配额（每日3次）。如需继续享受VIP服务，请及时续费。',
          showCancel: true,
          cancelText: '稍后',
          confirmText: '立即续费',
          success: (res) => {
            if (res.confirm) {
              // 跳转到VIP续费页面
              uni.navigateTo({
                url: '/pages/vip/upgrade?type=renew'
              })
            }
          }
        })
      }

    } catch (error) {
      console.error('处理VIP到期失败:', error)
    }
  }

  /**
   * 定期检查VIP状态
   */
  static async periodicVipCheck() {
    try {
      if (!this.isLoggedIn()) {
        return
      }

      const userInfo = this.getUserInfo()
      if (!userInfo) {
        return // 用户信息不存在
      }

      // 如果本地显示不是VIP，但有VIP过期时间，说明可能需要检查服务器状态
      const hasVipExpireTime = userInfo.vipExpireTime && userInfo.vipExpireTime !== null
      const isLocalVip = userInfo.isVip === 1

      if (!isLocalVip && !hasVipExpireTime) {
        return // 确实不是VIP用户，无需检查
      }

      // 防止频繁检查：如果距离上次检查不足5分钟，跳过
      const lastCheckTime = uni.getStorageSync('lastVipCheckTime')
      const now = Date.now()
      if (lastCheckTime && (now - lastCheckTime) < 5 * 60 * 1000) {
        return
      }

      // 记录本次检查时间
      uni.setStorageSync('lastVipCheckTime', now)

      // 检查本地VIP是否到期
      if (this.checkVipExpired()) {
        await this.handleVipExpired()
        return
      }

      // 调用后端API验证VIP状态
      const { checkVipStatus } = await import('../api/user.js')
      const userId = this.getCurrentUserId()

      if (userId) {
        const result = await checkVipStatus(userId)
        if (result && result.success) {
          const isVipValid = result.data.isVip
          const serverVipExpireTime = result.data.vipExpireTime

          if (!isVipValid && userInfo.isVip === 1) {
            // 后端显示VIP已过期，但本地还是VIP状态
            await this.handleVipExpired()
          } else if (isVipValid && userInfo.isVip !== 1) {
            // 后端显示VIP有效，但本地不是VIP状态（可能是数据不同步）
            this.updateUserField('isVip', 1)
            this.updateUserField('vipExpireTime', serverVipExpireTime)
            await this.refreshUserQuota()

            uni.$emit('userInfoUpdated', {
              isVip: true,
              vipExpireTime: serverVipExpireTime,
              reason: 'sync'
            })
          }
        }
      }
    } catch (error) {
      console.error('定期VIP检查失败:', error)
    }
  }

  /**
   * 强制检查VIP状态（不受防重复限制）
   * 用于用户主动进入设置页面等场景
   */
  static async forceCheckVipStatus() {
    try {
      if (!this.isLoggedIn()) {
        return false
      }

      const userInfo = this.getUserInfo()
      if (!userInfo) {
        return false
      }

      // 直接调用后端VIP状态检查接口
      const response = await uni.request({
        url: `${config.baseURL}/api/user/vip-status`,
        method: 'GET',
        data: { userId: userInfo.id },
        header: {
          'Authorization': `Bearer ${this.getToken()}`
        }
      })

      if (response.data && response.data.code === 200) {
        const vipData = response.data.data

        // 更新本地用户信息
        this.updateUserField('isVip', vipData.isVip ? 1 : 0)
        this.updateUserField('vipExpireTime', vipData.vipExpireTime)
        this.updateUserField('dailyQuota', vipData.dailyQuota)

        // 检查状态是否真的发生了变化
        const currentUserInfo = this.getUserInfo()
        const statusChanged = !currentUserInfo ||
          currentUserInfo.isVip !== (vipData.isVip ? 1 : 0) ||
          currentUserInfo.vipExpireTime !== vipData.vipExpireTime ||
          currentUserInfo.dailyQuota !== vipData.dailyQuota

        // 强制检查时总是刷新配额
        this.clearQuotaCache()
        await this.refreshUserQuota()

        // 只有状态真正发生变化时才通知页面更新
        if (statusChanged) {
          uni.$emit('userInfoUpdated', {
            isVip: vipData.isVip,
            vipExpireTime: vipData.vipExpireTime,
            dailyQuota: vipData.dailyQuota,
            reason: 'forceCheck'
          })
        }

        return vipData.isVip
      }

      return false
    } catch (error) {
      console.error('强制VIP状态检查失败:', error)
      return false
    }
  }
  
  /**
   * 获取用户显示名称
   * @returns {string} 显示名称
   */
  static getDisplayName() {
    const userInfo = this.getUserInfo()
    if (userInfo) {
      return userInfo.nickname || userInfo.username || 'yumu'
    }
    return '未登录'
  }
  
  /**
   * 获取用户头像
   * @returns {string} 头像URL
   */
  static getAvatar() {
    const userInfo = this.getUserInfo()
    if (userInfo && userInfo.avatar) {
      return userInfo.avatar
    }
    return '/static/images/default-avatar.png'
  }

  /**
   * 用户注册
   * @param {object} registerData - 注册数据
   * @returns {Promise} 注册结果
   */
  static async register(registerData) {
    try {
      // 调用注册API
      const result = await register(registerData)

      return {
        success: true,
        data: result,
        message: '注册成功'
      }
    } catch (error) {
      console.error('注册失败:', error)
      return {
        success: false,
        message: error.message || '注册失败，请重试'
      }
    }
  }
}

export default UserManager
