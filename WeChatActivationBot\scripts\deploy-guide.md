# 云服务器部署指南

## 服务器要求
- Linux服务器（Ubuntu/CentOS）
- Node.js 16+
- MySQL 5.7+
- 域名和SSL证书

## 部署步骤

### 1. 服务器环境准备
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装MySQL
sudo apt install mysql-server -y

# 安装PM2（进程管理）
sudo npm install -g pm2
```

### 2. 上传代码
```bash
# 使用git或scp上传代码到服务器
git clone your-repo-url
cd WeChatActivationBot

# 安装依赖
npm install
```

### 3. 配置数据库
```bash
# 登录MySQL
sudo mysql -u root -p

# 创建数据库和用户
CREATE DATABASE wechat_reply_bot CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'wechat_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON wechat_reply_bot.* TO 'wechat_user'@'localhost';
FLUSH PRIVILEGES;

# 导入数据库结构
mysql -u wechat_user -p wechat_reply_bot < database/init.sql
```

### 4. 配置环境变量
```bash
# 编辑.env文件
nano .env

# 更新配置
DB_HOST=localhost
DB_USER=wechat_user
DB_PASSWORD=your_password
SERVER_URL=https://your-domain.com
PORT=3001
```

### 5. 配置Nginx反向代理
```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/your/cert.pem;
    ssl_certificate_key /path/to/your/key.pem;
    
    location / {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 6. 启动服务
```bash
# 使用PM2启动
pm2 start src/app.js --name "wechat-bot"

# 设置开机自启
pm2 startup
pm2 save
```

### 7. 微信公众号配置
- URL: https://your-domain.com/wechat
- Token: wechat_activation_bot_2025
