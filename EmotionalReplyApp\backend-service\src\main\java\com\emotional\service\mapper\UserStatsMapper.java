package com.emotional.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.emotional.service.entity.UserStats;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDate;

/**
 * 用户统计Mapper接口
 */
@Mapper
public interface UserStatsMapper extends BaseMapper<UserStats> {

    /**
     * 获取用户指定日期的统计信息
     */
    @Select("SELECT * FROM user_stats WHERE user_id = #{userId} AND stat_date = #{statDate}")
    UserStats getUserStatsByDate(@Param("userId") Long userId, @Param("statDate") LocalDate statDate);

    /**
     * 获取用户总使用次数
     */
    @Select("SELECT COALESCE(SUM(daily_usage), 0) FROM user_stats WHERE user_id = #{userId}")
    Integer getTotalUsageByUserId(@Param("userId") Long userId);

    /**
     * 增加用户当日使用次数
     */
    @Update("UPDATE user_stats SET daily_usage = daily_usage + 1, updated_time = NOW() " +
            "WHERE user_id = #{userId} AND stat_date = #{statDate}")
    int incrementDailyUsage(@Param("userId") Long userId, @Param("statDate") LocalDate statDate);

    /**
     * 获取用户当日使用次数
     */
    @Select("SELECT COALESCE(daily_usage, 0) FROM user_stats WHERE user_id = #{userId} AND stat_date = #{statDate}")
    Integer getDailyUsage(@Param("userId") Long userId, @Param("statDate") LocalDate statDate);

    /**
     * 更新用户配额（VIP激活）
     */
    @Update("UPDATE user_stats SET daily_quota = #{quota}, is_vip = 1, updated_time = NOW() " +
            "WHERE user_id = #{userId}")
    int updateUserQuotaByUserId(@Param("userId") Long userId, @Param("quota") Integer quota);

    /**
     * 更新用户配额（VIP过期）
     */
    @Update("UPDATE user_stats SET daily_quota = #{quota}, is_vip = 0, updated_time = NOW() " +
            "WHERE user_id = #{userId}")
    int updateUserQuotaAfterExpired(@Param("userId") Long userId, @Param("quota") Integer quota);

    /**
     * 批量更新过期VIP用户的统计表配额
     * 基于过期时间查找需要降级的用户，在users表更新之前执行
     */
    @Update("UPDATE user_stats SET daily_quota = 3, is_vip = 0, updated_time = NOW() " +
            "WHERE user_id IN (SELECT id FROM users WHERE is_vip = 1 AND vip_expire_time IS NOT NULL AND vip_expire_time <= NOW() AND deleted = 0)")
    int batchUpdateExpiredVipUsersStats();
}
