#!/usr/bin/env node

const crypto = require('crypto');
const axios = require('axios');
require('dotenv').config();

/**
 * 微信公众号配置诊断工具
 */

async function diagnoseWeChatConfig() {
    console.log('🔍 微信公众号配置诊断\n');
    
    const config = {
        token: process.env.WECHAT_TOKEN,
        appid: process.env.WECHAT_APPID,
        appsecret: process.env.WECHAT_APPSECRET,
        port: process.env.PORT || 3001
    };
    
    console.log('📋 配置检查:');
    console.log(`   Token: ${config.token}`);
    console.log(`   AppID: ${config.appid}`);
    console.log(`   AppSecret: ${config.appsecret ? '已配置' : '未配置'}`);
    console.log(`   端口: ${config.port}\n`);
    
    // 1. 检查本地服务
    console.log('🔧 1. 检查本地服务状态...');
    try {
        const response = await axios.get(`http://localhost:${config.port}/health`, {
            timeout: 5000
        });
        console.log('   ✅ 本地服务正常运行');
        console.log(`   响应: ${JSON.stringify(response.data)}\n`);
    } catch (error) {
        console.log('   ❌ 本地服务无法访问');
        console.log(`   错误: ${error.message}\n`);
        return false;
    }
    
    // 2. 检查微信验证接口
    console.log('🔧 2. 检查微信验证接口...');
    try {
        const timestamp = Math.floor(Date.now() / 1000).toString();
        const nonce = 'test_nonce_' + Date.now();
        const echostr = 'test_echo_' + Date.now();
        
        // 生成正确的签名
        const signature = crypto.createHash('sha1')
            .update([config.token, timestamp, nonce].sort().join(''))
            .digest('hex');
        
        const url = `http://localhost:${config.port}/wechat?signature=${signature}&timestamp=${timestamp}&nonce=${nonce}&echostr=${echostr}`;
        
        const response = await axios.get(url, { timeout: 5000 });
        
        if (response.data === echostr) {
            console.log('   ✅ 微信验证接口正常');
            console.log(`   返回: ${response.data}\n`);
        } else {
            console.log('   ❌ 微信验证接口返回错误');
            console.log(`   期望: ${echostr}`);
            console.log(`   实际: ${response.data}\n`);
        }
    } catch (error) {
        console.log('   ❌ 微信验证接口异常');
        console.log(`   错误: ${error.message}\n`);
    }
    
    // 3. 检查网络连通性
    console.log('🔧 3. 检查网络连通性...');
    try {
        const response = await axios.get('https://api.weixin.qq.com/cgi-bin/token', {
            params: {
                grant_type: 'client_credential',
                appid: config.appid,
                secret: config.appsecret
            },
            timeout: 10000
        });
        
        if (response.data.access_token) {
            console.log('   ✅ 微信API连接正常');
            console.log('   ✅ AppID和AppSecret有效\n');
        } else {
            console.log('   ❌ 微信API返回错误');
            console.log(`   错误: ${JSON.stringify(response.data)}\n`);
        }
    } catch (error) {
        console.log('   ❌ 无法连接微信API');
        console.log(`   错误: ${error.message}\n`);
    }
    
    // 4. 提供解决方案
    console.log('💡 解决方案:');
    console.log('');
    console.log('如果配置超时，通常是以下原因：');
    console.log('');
    console.log('1. 🌐 公网访问问题（最常见）');
    console.log('   - 微信服务器无法访问您的本地服务器');
    console.log('   - 解决方案：使用ngrok或部署到云服务器');
    console.log('   - 运行: npm run start-ngrok');
    console.log('');
    console.log('2. 🔥 防火墙阻拦');
    console.log('   - Windows防火墙可能阻拦了端口3001');
    console.log('   - 解决方案：在防火墙中允许Node.js或端口3001');
    console.log('');
    console.log('3. 🔧 Token配置错误');
    console.log('   - 当前Token: ucan2025_fighting_yumu_2025');
    console.log('   - 确保微信公众平台配置的Token完全一致');
    console.log('');
    console.log('4. ⏱️ 响应超时');
    console.log('   - 微信验证有5秒超时限制');
    console.log('   - 确保服务器响应速度足够快');
    console.log('');
    
    return true;
}

// 生成ngrok配置建议
function generateNgrokConfig() {
    console.log('🚀 ngrok配置步骤:');
    console.log('');
    console.log('1. 安装ngrok:');
    console.log('   - 访问 https://ngrok.com/');
    console.log('   - 注册账号并下载ngrok');
    console.log('   - 配置authtoken: ngrok config add-authtoken YOUR_TOKEN');
    console.log('');
    console.log('2. 启动服务:');
    console.log('   - 终端1: node src/app.js');
    console.log('   - 终端2: ngrok http 3001');
    console.log('');
    console.log('3. 配置微信公众号:');
    console.log('   - URL: https://xxxxx.ngrok.io/wechat');
    console.log('   - Token: ucan2025_fighting_yumu_2025');
    console.log('   - EncodingAESKey: 随机生成');
    console.log('   - 消息加解密方式: 明文模式');
    console.log('');
}

// 主函数
async function main() {
    const args = process.argv.slice(2);
    
    if (args.includes('--help') || args.includes('-h')) {
        console.log('🔍 微信公众号配置诊断工具');
        console.log('');
        console.log('使用方法:');
        console.log('  node scripts/diagnose-wechat.js        # 完整诊断');
        console.log('  node scripts/diagnose-wechat.js ngrok  # ngrok配置指南');
        console.log('');
        return;
    }
    
    if (args.includes('ngrok')) {
        generateNgrokConfig();
    } else {
        await diagnoseWeChatConfig();
        console.log('');
        generateNgrokConfig();
    }
}

if (require.main === module) {
    main().catch(error => {
        console.error('诊断失败:', error);
        process.exit(1);
    });
}

module.exports = { diagnoseWeChatConfig, generateNgrokConfig };
