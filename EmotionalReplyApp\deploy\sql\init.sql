-- 情感回复助手数据库初始化脚本
-- 创建时间: 2025-01-01

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `emotional_reply` 
DEFAULT CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE `emotional_reply`;

-- 用户表
CREATE TABLE IF NOT EXISTS `users` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `is_vip` tinyint(1) DEFAULT 0 COMMENT '是否VIP用户',
  `vip_expire_time` datetime DEFAULT NULL COMMENT 'VIP过期时间',
  `daily_quota` int(11) DEFAULT 3 COMMENT '每日配额',
  `is_admin` tinyint(1) DEFAULT 0 COMMENT '是否管理员',
  `status` tinyint(1) DEFAULT 0 COMMENT '用户状态 0-正常 1-禁用',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`),
  KEY `idx_is_vip` (`is_vip`),
  KEY `idx_vip_expire_time` (`vip_expire_time`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 用户统计表
CREATE TABLE IF NOT EXISTS `user_stats` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `today_usage` int(11) DEFAULT 0 COMMENT '今日使用次数',
  `total_usage` int(11) DEFAULT 0 COMMENT '总使用次数',
  `remaining_quota` int(11) DEFAULT 3 COMMENT '剩余配额',
  `last_reset_date` date DEFAULT NULL COMMENT '最后重置日期',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_last_reset_date` (`last_reset_date`),
  CONSTRAINT `fk_user_stats_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户统计表';

-- VIP激活码表
CREATE TABLE IF NOT EXISTS `vip_activation_codes` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '激活码ID',
  `code` varchar(32) NOT NULL COMMENT '激活码',
  `code_type` varchar(20) DEFAULT 'vip' COMMENT '激活码类型',
  `duration_days` int(11) NOT NULL COMMENT '有效天数',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态 0-未使用 1-已使用 2-已过期',
  `used_by` bigint(20) DEFAULT NULL COMMENT '使用者用户ID',
  `used_time` datetime DEFAULT NULL COMMENT '使用时间',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建者ID',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_status` (`status`),
  KEY `idx_used_by` (`used_by`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='VIP激活码表';

-- 消息历史表
CREATE TABLE IF NOT EXISTS `message_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `original_message` text NOT NULL COMMENT '原始消息',
  `reply_content` text NOT NULL COMMENT '回复内容',
  `emotion_analysis` json DEFAULT NULL COMMENT '情感分析结果',
  `reply_style` varchar(50) DEFAULT NULL COMMENT '回复风格',
  `generation_mode` varchar(20) DEFAULT 'smart' COMMENT '生成模式',
  `processing_time` int(11) DEFAULT NULL COMMENT '处理时间(毫秒)',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_time` (`created_time`),
  CONSTRAINT `fk_message_history_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息历史表';

-- 系统配置表
CREATE TABLE IF NOT EXISTS `system_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_type` varchar(20) DEFAULT 'string' COMMENT '配置类型',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `is_public` tinyint(1) DEFAULT 0 COMMENT '是否公开配置',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 插入默认系统配置
INSERT INTO `system_config` (`config_key`, `config_value`, `config_type`, `description`, `is_public`) VALUES
('app.name', '情感回复助手', 'string', '应用名称', 1),
('app.version', '1.0.0', 'string', '应用版本', 1),
('user.default_quota', '3', 'integer', '用户默认每日配额', 0),
('vip.default_quota', '50', 'integer', 'VIP用户默认每日配额', 0),
('app.maintenance_mode', 'false', 'boolean', '维护模式', 1);

-- 创建默认管理员用户 (密码: admin123)
INSERT INTO `users` (`username`, `email`, `password`, `nickname`, `is_admin`, `is_vip`, `daily_quota`) VALUES
('admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBZWvsUMgzTSji', '系统管理员', 1, 1, 999);

-- 为管理员创建统计记录
INSERT INTO `user_stats` (`user_id`, `remaining_quota`) VALUES (1, 999);

SET FOREIGN_KEY_CHECKS = 1;
